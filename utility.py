import os
from typing import Dict, Any
import json

def load_toyota_config():
    """Load Toyota car configuration from main menu and car files"""
    config = {}
    
    # Load main menu first
    try:
        if os.path.exists('main_menu.json'):
            with open('main_menu.json', 'r', encoding='utf-8') as f:
                main_config = json.load(f)
                config.update(main_config)
                print(f"Loaded main_menu.json")
        else:
            print(f"main_menu.json not found")
            
    except json.JSONDecodeError as e:
        print(f"Error parsing main_menu.json: {e}")
    except Exception as e:
        print(f"Error loading main_menu.json: {e}")

    # Load Toyota car files from cars directory
    cars_dir = 'cars'
    if os.path.exists(cars_dir) and os.path.isdir(cars_dir):
        print(f"Loading Toyota cars from {cars_dir} directory...")
        for filename in os.listdir(cars_dir):
            if filename.endswith('.json'):
                try:
                    file_path = os.path.join(cars_dir, filename)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        car_config = json.load(f)

                        # Extract car name from filename (e.g., Camry.json -> Camry)
                        car_name = filename.replace('.json', '')

                        # Add all keys from the car config to main config
                        for key, value in car_config.items():
                            config[key] = value
                            
                        print(f"Loaded {car_name} configuration")

                except Exception as e:
                    print(f"Error loading cars/{filename}: {e}")
    else:
        print(f"Cars directory not found: {cars_dir}")

    return config

# Load Toyota configuration
WHATSAPP_CONFIG = load_toyota_config()

def list_all_toyota_cars() -> list:
    """
    Get a list of all Toyota cars available

    Returns:
        list: List of Toyota car names
    """
    toyota_cars = []
    
    # Get car names from the cars directory
    cars_dir = 'cars'
    if os.path.exists(cars_dir) and os.path.isdir(cars_dir):
        for filename in os.listdir(cars_dir):
            if filename.endswith('.json') and filename != 'cars_data.py':
                car_name = filename.replace('.json', '')
                toyota_cars.append(car_name)
    
    return sorted(toyota_cars)

def load_text_file(filename: str) -> str:
    """
    Loads text content from a specified file.

    Args:
        filename (str): The path to the text file.

    Returns:
        str: The content of the file, or an empty string if not found/error.
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as file:
                return file.read().strip()
        else:
            print(f"Warning: File {filename} not found")
            return ""
    except Exception as e:
        print(f"Error loading file {filename}: {e}")
        return ""


def format_all_cars_message(all_cars_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a human-friendly message and WhatsApp-friendly buttons for a list of cars.

    Args:
        all_cars_result (dict): Result from get_all_cars() containing 'cars' list

    Returns:
        dict: Response with function_response format
    """
    cars = all_cars_result.get('cars', []) if isinstance(all_cars_result, dict) else []

    if not cars:
        return {
            "generated_text": "❌ No cars available at the moment.",
            "function_response_id": 1,
            "function_response": [{
                "data": [],
                "data_type": "list",
                "message": "❌ No cars available at the moment."
            }]
        }

    # Get car names and filter out any non-string values
    car_names = []
    for car in cars:
        if isinstance(car, dict):
            name = car.get('name')
            if isinstance(name, str):
                car_names.append(name)
        elif isinstance(car, str):
            car_names.append(car)

    if not car_names:
        return {
            "generated_text": "❌ Error retrieving car names.",
            "function_response_id": 1,
            "function_response": [{
                "data": [],
                "data_type": "list",
                "message": "❌ Error retrieving car names."
            }]
        }

    # Filter out "Urban Cruiser" prefix and take only 10 cars
    filtered_names = []
    for name in sorted(car_names):
        # Remove "Urban Cruiser" prefix
        clean_name = name.replace("Urban Cruiser ", "")
        filtered_names.append(clean_name)
    
    # Take only the first 10 cars
    car_names = filtered_names[:10]
    
    welcome_message = (
        "We have a wide range of Toyota cars available, from fuel-efficient hatchbacks "
        "to premium SUVs and luxury sedans. To help you find the perfect car, I can show "
        "you all available models, or you can narrow it down by category.\n\n"
        "* View All Cars: See our complete inventory of Toyota cars.\n"
        "* Browse by Category: Explore cars by type, such as SUVs, hatchbacks, or sedans.\n\n"
        "Which option would you like to choose?\n\n"
        "📋 Here are some vehicles you can explore. Please select one:"
    )
    
    return {
        "generated_text": welcome_message,
        "function_response_id": 1,
        "function_response": [{
            "data": car_names,
            "data_type": "list",
            "message": welcome_message
        }]
    }


def format_car_details_from_data(car_data: Dict[str, Any], _context: Dict[str, Any], _opts: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a single car's data into a WhatsApp-friendly message and action buttons.

    Args:
        car_data (dict): Car specification dictionary
        _context (dict): Unused - kept for compatibility with callers
        _opts (dict): Unused - kept for compatibility with callers

    Returns:
        dict: Formatted response with keys like 'message' and 'buttons'
    """
    if not car_data or not isinstance(car_data, dict):
        return {"status": "error", "message": "Car data not available", "buttons": []}

    name = car_data.get('name') or car_data.get('model') or 'Unknown Model'
    category = car_data.get('category', '')
    fuel = ', '.join(car_data.get('fuel_types', [])) if car_data.get('fuel_types') else car_data.get('fuel_type', '')

    # Try to get a sensible price or variant summary
    price = car_data.get('price') or car_data.get('starting_price') or ''
    if not price:
        # Try variants
        variants = car_data.get('variants') or []
        if variants and isinstance(variants, list) and len(variants) > 0:
            first_variant = variants[0]
            price = first_variant.get('price') or first_variant.get('ex_showroom') or ''

    # Build short spec lines
    specs = []
    if category:
        specs.append(f"Category: {category}")
    if fuel:
        specs.append(f"Fuel: {fuel}")
    if price:
        specs.append(f"Price: {price}")

    # Additional common fields
    if car_data.get('seating_capacity'):
        specs.append(f"Seating: {car_data.get('seating_capacity')}")
    if car_data.get('engine'):
        specs.append(f"Engine: {car_data.get('engine')}")
    if car_data.get('mileage'):
        specs.append(f"Mileage: {car_data.get('mileage')}")

    header = f"🚗 {name}\n"
    if specs:
        header += "\n" + " • ".join(specs)

    # Short description if available
    description = car_data.get('short_description') or car_data.get('description') or ''
    if description:
        message = f"{header}\n\n{description}"
    else:
        message = header

    # Prepare common car action buttons
    actions = []
    # Variant and media/gallery actions
    actions.append(f"{name} Variants")
    actions.append("Exterior")
    actions.append("Interior")
    actions.append("Gallery")
    actions.append("Request Brochure")
    actions.append("Book Test Drive")
    actions.append("Request a Call Back")

    buttons = [{
        "data": actions,
        "data_type": "button",
        "message": message
    }]

    response = {"status": "success", "message": message, "buttons": buttons}

    # Attach media_id if present in data
    if car_data.get('Media_ID'):
        response['media_id'] = car_data.get('Media_ID')

    return response