#!/usr/bin/env python3
"""
Toyota Car Details Handler
Handles car detail requests and queries
"""

import json
from typing import Dict, Any
from car_data_manager import get_car_data, get_all_cars

def get_car_details_response(car_name: str, user_id: str) -> Dict[str, Any]:
    """
    Get car details response directly from car data
    
    Args:
        car_name: Name of the car (e.g., "Glanza")
        user_id: User ID for the request
        
    Returns:
        Dict with response data
    """
    try:
        # Normalize car name
        car_name = car_name.strip().title()
        
        # Get car data
        car_data = get_car_data(car_name)
        
        if not car_data:
            return {
                "text": f"❌ Sorry, I couldn't find information about {car_name}. Please check the car name and try again.",
                "llm": f"❌ Sorry, I couldn't find information about {car_name}. Please check the car name and try again.",
                "function_response_id": 1,
                "functin_response": []
            }
        
        # Try to get "More about this Car" section
        details_key = "More about this Car"
        if details_key in car_data:
            details_section = car_data[details_key][0]
            message = details_section.get('message', '')
            data_options = details_section.get('data', [])
            
            # Format the response
            response_text = f"🚗 *{car_name.upper()} DETAILS*\n\n{message}"
            
            return {
                "text": response_text,
                "llm": response_text,
                "function_response_id": 1,
                "functin_response": [{
                    "data": data_options,
                    "data_type": details_section.get('data_type', 'list')
                }]
            }
        
        # If no detailed section, try to get basic info from main car section
        elif car_name in car_data:
            main_section = car_data[car_name][0]
            message = main_section.get('message', f'{car_name} information')
            data_options = main_section.get('data', [])
            
            response_text = f"🚗 *{car_name.upper()}*\n\n{message}"
            
            return {
                "text": response_text,
                "llm": response_text,
                "function_response_id": 1,
                "functin_response": [{
                    "data": data_options,
                    "data_type": main_section.get('data_type', 'list')
                }]
            }
        
        else:
            return {
                "text": f"❌ Sorry, detailed information for {car_name} is not available at the moment.",
                "llm": f"❌ Sorry, detailed information for {car_name} is not available at the moment.",
                "function_response_id": 1,
                "functin_response": []
            }
            
    except Exception as e:
        return {
            "text": f"❌ Sorry, there was an error retrieving {car_name} details. Please try again later.",
            "llm": f"❌ Sorry, there was an error retrieving {car_name} details. Please try again later.",
            "function_response_id": 1,
            "functin_response": []
        }

def handle_car_query(prompt: str, user_id: str) -> Dict[str, Any]:
    """
    Handle car-related queries directly
    
    Args:
        prompt: User's query
        user_id: User ID
        
    Returns:
        Response dictionary
    """
    prompt_lower = prompt.lower().strip()
    
    # Direct response with car list if asking about available cars
    if any(phrase in prompt_lower for phrase in ["all cars", "show cars", "list cars", "available cars", "what cars"]):
        return get_all_cars_response(user_id)
    
    # List of Toyota cars
    toyota_cars = [
        "Camry", "Fortuner", "Glanza", "Hilux", "Hyryder",
        "Innova Crysta", "Innova Hycross", "Land Cruiser", 
        "Legender", "Rumion", "Taisor", "Vellfire"
    ]
    
    # Check if user is asking for car details
    detail_keywords = ["details", "detail", "about", "info", "information", "specs", "specification", "tell me"]
    
    # Find which car they're asking about
    mentioned_car = None
    for car in toyota_cars:
        if car.lower() in prompt_lower:
            mentioned_car = car
            break
        # Also check without spaces (e.g., "innovacrystal" -> "Innova Crysta")
        car_no_space = car.replace(" ", "").lower()
        if car_no_space in prompt_lower.replace(" ", ""):
            mentioned_car = car
            break
    
    # Check if it's a details request
    is_details_request = any(keyword in prompt_lower for keyword in detail_keywords)
    
    if mentioned_car and is_details_request:
        return get_car_details_response(mentioned_car, user_id)
    
    # If no specific car is mentioned or not asking for details, show all cars
    if not mentioned_car or not is_details_request:
        return get_all_cars_response(user_id)
    
    # If specific car is mentioned with details request
    return get_car_details_response(mentioned_car, user_id)

def get_all_cars_response(user_id: str) -> Dict[str, Any]:
    """Get response showing all Toyota cars"""
    # Get car data from car_data_manager
    cars = get_all_cars()
    toyota_cars = []
    car_details = {}

    # Extract car names and categorize them
    for car_key, car_data in cars.items():
        car_name = car_data.get("name", "")
        if car_name:
            toyota_cars.append(car_name)
            car_details[car_name] = {
                "category": car_data.get("category", ""),
                "variants": car_data.get(car_key + " variants", [])
            }

    message = "🚗 *ALL TOYOTA CARS*\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━\n"
    message += f"📊 Total: *{len(toyota_cars)}* cars available\n\n"
    
    for i, car in enumerate(sorted(toyota_cars), 1):
        details = car_details.get(car, {})
        category = details.get("category", "")
        
        # Determine emoji based on category
        if 'SUV' in category:
            emoji = "🚙"  # SUV
        elif any(word in category for word in ['Sedan', 'Premium', 'Luxury']):
            emoji = "�"  # Sedan/Premium
        elif any(word in category for word in ['MPV', 'MUV']):
            emoji = "�"  # MPV
        elif 'Truck' in category:
            emoji = "🛻"  # Pickup Truck
        elif 'Hatchback' in category:
            emoji = "�"  # Hatchback
        else:
            emoji = "🚗"  # Default
            
        # Add starting price if available
        price_text = ""
        variants = details.get("variants", [])
        if variants:
            try:
                base_variant = next(v for v in variants if "price" in v)
                if base_variant:
                    price_text = f" (Starting {base_variant['price']})"
            except (StopIteration, KeyError):
                pass
            
        message += f"  {i:2d}. {emoji} *{car}* - {category}{price_text}\n"
    
    message += "\n👇 *Select any car for detailed information*"
    
    return {
        "text": message,
        "llm": message,
        "function_response_id": 1,
        "functin_response": [{
            "data": toyota_cars,
            "data_type": "list"
        }]
    }