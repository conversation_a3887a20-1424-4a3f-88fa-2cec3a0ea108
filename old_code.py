import difflib
import re
import os
import logging
import time
import sys
import json
from typing import Optional
import hashlib
import hmac
from pydantic import BaseModel
import uvicorn
from datetime import datetime
from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException, logger
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import google.generativeai as genai

from car_data_manager import get_car_data
import car_data_manager
# Import car data from cars_data.py
# Load Python-side cars data (CARS_DATA) if available. Use safe fallback to avoid NameError.
try:
    from cars.cars_data import CARS_DATA
except Exception:
    CARS_DATA = {}
from json_load import CAR_DATA_REGISTRY, CAR_NAME_MAPPINGS, MAIN_MENU_DATA, create_enhanced_system_prompt, get_car_data_by_name, load_information_file, load_system_prompt
# Import unified car system
from sheet import store_meta_form_data
from tool_def import TOOL_DEF
from toyota_database import insert_chat_message, insert_liveagentFlag, parse_user_id, sync_create_live_chat_session, sync_insert_chat_message, sync_insert_service_booking, sync_insert_user_interaction, update_handover
from utility import WHATSAPP_CONFIG, format_all_cars_message, format_car_details_from_data
import utility



base_system_prompt = load_system_prompt()
# Load knowledge base and information
knowledge_base_content = load_information_file()

# Create enhanced system prompt with knowledge base
system_prompt = create_enhanced_system_prompt(base_system_prompt, knowledge_base_content)
load_dotenv()
app = FastAPI()

# Configure CORS (allow all origins similar to previous Flask CORS default)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')


# Initialize Gemini AI
model = None
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel(
            model_name="models/gemini-2.0-flash",
            tools=TOOL_DEF,
            system_instruction=system_prompt
        )
        logger.info("✅ Gemini AI model initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize Gemini AI model: {e}")
        model = None
else:
    logger.warning("⚠️ GEMINI_API_KEY not found in environment variables")
SESSION_ID = {}
# Store user context for each session
USER_CONTEXT = {}

# Performance monitoring removed to fix import issues

# Simple in-memory session storage for tracking user's last viewed car
USER_CAR_CONTEXT = {}
def update_user_car_context(user_id, car_name):
    """Update the user's car context when they view a car"""
    if user_id and car_name and car_name != 'Not specified':
        USER_CAR_CONTEXT[user_id] = car_name
        logger.info(f"🔄 Updated car context for user {user_id}: {car_name}")

def get_user_car_context(user_id):
    """Get the user's last viewed car"""
    return USER_CAR_CONTEXT.get(user_id, 'Not specified')

def get_price_key_for_car(car_data):
    """
    Get the correct price key for a car's variant data
    Handles different naming conventions across JSON files
    """
    possible_price_keys = [
        'Ex-showroom Price',      
        'Ex-showroom Price',      
        'Ex-Showroom Prices',     
        'Ex-Showroom Prices',     
        'Variants',             
        'Price List'              
    ]

    for key in possible_price_keys:
        if key in car_data:
            return key

    return None


def get_all_car_names():
    """Get all available car names for fuzzy matching"""
    car_names = []

    # Add from CAR_DATA_REGISTRY
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name != 'Main Menu':
            car_names.append(car_name.lower())

    # Add from CAR_NAME_MAPPINGS
    for mapping, canonical in CAR_NAME_MAPPINGS.items():
        car_names.append(mapping.lower())
        car_names.append(canonical.lower())

    # Add common car names
    common_names = [
        'Camry', 'Fortuner', 'Vellfire','Glanza','Hyryder',
        'Hilux', 'Rumion', 'Taisor','Vellfire','Innova_Crysta',
        'Innova_Hycross','Land_Cruiser','Legender'
    ]
    car_names.extend(common_names)

    return list(set(car_names))

def search_knowledge_base(query):
    """Search knowledge base for specific information - simplified approach"""
    if not knowledge_base_content:
        return None

    query_lower = query.lower()
    lines = knowledge_base_content.split('\n')

    # Look for relevant Q&A pairs with scoring for better matching
    best_match = None
    best_score = 0

    for i, line in enumerate(lines):
        if line.startswith('Q:'):
            line_lower = line.lower()

            # Calculate relevance score
            score = 0
            query_words = query_lower.split()

            # Score for other matching words
            for word in query_words:
                if len(word) > 2 and word in line_lower:  # Skip short words like "of", "is"
                    score += 1

            # If this is a better match, store it
            if score > best_score and score > 2:  # Minimum threshold
                if i + 1 < len(lines) and lines[i + 1].startswith('A:'):
                    question = line[2:].strip()  # Remove 'Q:'
                    answer = lines[i + 1][2:].strip()  # Remove 'A:'

                    # Get additional lines that are part of the answer
                    full_answer = answer
                    j = i + 2
                    while j < len(lines) and not lines[j].startswith('Q:') and lines[j].strip():
                        full_answer += '\n' + lines[j].strip()
                        j += 1

                    best_match = {
                        "status": "success",
                        "message": f"📍 **{question}**\n\n{full_answer}",
                        "source": "knowledge_base"
                    }
                    best_score = score

    return best_match



def is_specific_model_name(name):
    """
    Check if a name represents a specific vehicle model (not a category)

    Args:
        name (str): Name to check

    Returns:
        bool: True if it's a specific model, False if it's a category
    """
    if not name or name == 'Not specified':
        return False

    # Categories that are NOT specific models
    categories = [
        'Main Menu', 'Cars', 
    ]

    return name not in categories

def get_model_name_from_context(user_id, fallback_name=None):
    """
    Get the most specific model name from user context or current interaction

    Args:
        user_id (str): WhatsApp user ID
        fallback_name (str): Fallback name if no context found

    Returns:
        str: Most specific model name available
    """
    try:
        # First check if fallback name is a specific model (from car-specific actions)
        if fallback_name and is_specific_model_name(fallback_name):
            logger.info(f"🎯 Using specific model from action: {fallback_name}")
            return fallback_name

        # Then try to get from user car context
        car_context = get_user_car_context(user_id)
        if car_context and is_specific_model_name(car_context):
            logger.info(f"🎯 Using model from user context: {car_context}")
            return car_context

        # If no specific model found, return fallback or 'Not specified'
        result = fallback_name if fallback_name else 'Not specified'
        logger.info(f"🎯 No specific model found, using: {result}")
        return result

    except Exception as e:
        logger.error(f"❌ Error getting model name from context: {e}")
        return fallback_name or 'Not specified'

def handle_test_drive_callback_request(action_type, user_id, car_name=None):
    """
    Handle test drive and callback requests by saving user data to Google Sheet and Database

    Args:
        action_type (str): 'test_drive' or 'call_back'
        user_id (str): WhatsApp user ID (phone number)
        car_name (str): Name of the car (optional)

    Returns:
        bool: True if successfully saved, False otherwise
    """
    try:
        from sheet import store_test_drive_callback_data

        # Parse user ID for database operations
        actual_user_id, session_id, project_id = parse_user_id(user_id)

        # Get the most specific model name available
        model_name = get_model_name_from_context(user_id, car_name)

        # Prepare data for sheet
        request_data = {
            'user_id': user_id,
            'phone_number': user_id,  # WhatsApp user_id is the phone number
            'request_type': action_type,
            'car_name': model_name,
            'timestamp': datetime.now().isoformat(),
            'status': 'Pending'
        }

        # Store in Google Sheet
        sheet_success = store_test_drive_callback_data(request_data)

        # Store in Database
        db_success = False
        try:
            customer_data = {
                'name': 'Not provided',
                'phone': user_id,
                'email': 'Not provided',
                'car_model': model_name,
                'message': f'WhatsApp {action_type.replace("_", " ").title()} request'
            }

            booking_id = sync_insert_service_booking(
                actual_user_id,
                session_id,
                action_type,
                customer_data
            )

            if booking_id:
                logger.info(f"✅ {action_type.title()} request saved to database with ID {booking_id}")
                # Log the interaction
                sync_insert_user_interaction(
                    actual_user_id,
                    session_id,
                    f"{action_type}_request",
                    {"booking_id": booking_id, "car_name": model_name},
                    model_name
                )
                db_success = True
            else:
                logger.warning(f"⚠️ Failed to save {action_type} request to database")

        except Exception as db_error:
            logger.warning(f"Database booking failed: {db_error}")

        if sheet_success:
            logger.info(f"✅ {action_type.title()} request saved to Google Sheet for user {user_id}, model: {model_name}")
        else:
            logger.error(f"❌ Failed to save {action_type} request to Google Sheet for user {user_id}")

        # Return True if either storage method succeeded
        return sheet_success or db_success

    except Exception as e:
        logger.error(f"❌ Error handling {action_type} request: {e}")
        return False
    
def get_main_menu_message(action_name):
    """
    Get message from main_menu.json for specific actions

    Args:
        action_name (str): Name of the action (e.g., "Book a Test Drive", "Request a Callback")

    Returns:
        str: Message from main_menu.json or default message
    """
    try:
        # Load main menu data
        import json
        with open('main_menu.json', 'r', encoding='utf-8') as f:
            main_menu_data = json.load(f)

        # Get the message for the specific action
        if action_name in main_menu_data:
            action_data = main_menu_data[action_name]
            if isinstance(action_data, list) and len(action_data) > 0:
                return action_data[0].get('message', f"Thank you for your {action_name.lower()} request.")

        # Fallback messages
        if 'test' in action_name.lower() and 'drive' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for booking a test drive. Our dealer will contact you SOON."
        elif 'call_back' in action_name.lower() or 'call back' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for requesting a call back. Our dealer will contact you SOON."
        else:
            return f"Thank you for your {action_name.lower()} request."

    except Exception as e:
        logger.error(f"❌ Error loading main menu message for {action_name}: {e}")
        # Fallback messages
        if 'test' in action_name.lower() and 'drive' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for booking a test drive. Our dealer will contact you SOON."
        elif 'call_back' in action_name.lower() or 'call back' in action_name.lower():
            return "📝 *Disclaimer:* Thank you for requesting a call back. Our dealer will contact you SOON."
        else:
            return f"Thank you for your {action_name.lower()} request."
    

def get_car_response_with_media_id(car_name, action_key=None, user_id=None):
    """Get car response with media_id support and handle special actions"""
    car_data = get_car_data_by_name(car_name)

    if not car_data:
        return None

    # Get the proper car name (handle name mappings)
    proper_car_name = car_name
    if car_name in CAR_DATA_REGISTRY:
        proper_car_name = car_name
    else:
        # Check if it's a mapped name
        normalized_name = car_name.lower().strip()
        if normalized_name in CAR_NAME_MAPPINGS:
            proper_car_name = CAR_NAME_MAPPINGS[normalized_name]

    # If action_key is provided, look for specific action
    if action_key and action_key in car_data:
        response_data = car_data[action_key]
    # Otherwise, look for the main car entry using proper name
    elif proper_car_name in car_data:
        response_data = car_data[proper_car_name]
    else:
        # Fallback: get first available entry
        first_key = next(iter(car_data.keys()), None)
        if first_key:
            response_data = car_data[first_key]
        else:
            return None

    if not response_data or not isinstance(response_data, list) or len(response_data) == 0:
        return None

    # Handle multi-item sections - ANY response with multiple items should be handled properly
    if len(response_data) > 1:
        # For multi-item sections, combine the data from both items
        first_item = response_data[0]
        second_item = response_data[1] if len(response_data) > 1 else {}

        # Get data from first item
        first_message = first_item.get("message", "")
        first_data = first_item.get("data", [])
        first_data_type = first_item.get("data_type", "button")
        media_id = first_item.get("Media_ID", "")

        # Get data from second item
        second_message = second_item.get("message", "")
        second_data = second_item.get("data", [])
        second_data_type = second_item.get("data_type", "list")

        # For Main Menu, combine messages properly
        if proper_car_name == 'Main Menu':
            combined_message = f"{first_message}\n\n{second_message}"
        else:
            # For gallery sections (Exterior, Interior), avoid duplicating the "click below" message
            # Only use the first message to avoid duplication
            combined_message = first_message

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": combined_message,
            "data": first_data,
            "data_type": first_data_type,
            "buttons": [{
                "data": first_data,
                "data_type": first_data_type,
                "message": first_message
            }, {
                "data": second_data,
                "data_type": second_data_type,
                "message": second_message
            }],
            "hasButtons": len(second_data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    else:
        # Handle regular sections (single item)
        step_config = response_data[0]
        message = step_config.get("message", "")
        data = step_config.get("data", [])
        data_type = step_config.get("data_type", "list")
        media_id = step_config.get("Media_ID", "")  # Extract Media_ID if present

        response = {
            "status": "success",
            "car_name": proper_car_name,
            "message": message,
            "data": data,
            "data_type": data_type,
            "buttons": [{
                "data": data,
                "data_type": data_type,
                "message": message
            }],
            "hasButtons": len(data) > 0
        }

        # Add media_id if present
        if media_id:
            response["media_id"] = media_id
            response["buttons"][0]["media_id"] = media_id

    # Update user's car context when viewing a car
    if proper_car_name and user_id and proper_car_name != 'Not specified':
        update_user_car_context(user_id, proper_car_name)

    # Handle special actions: Book Test Drive and Request a Call Back
    if action_key and user_id:
        action_lower = action_key.lower()
        if 'test' in action_lower and 'drive' in action_lower:
            # Handle Book Test Drive
            handle_test_drive_callback_request('test_drive', user_id, proper_car_name)
            # Use message from main_menu.json
            response["message"] = get_main_menu_message("Book a Test Drive")

        elif 'call' in action_lower and 'back' in action_lower:
            # Handle Request a Call Back
            handle_test_drive_callback_request('call_back', user_id, proper_car_name)
            # Use message from main_menu.json
            response["message"] = get_main_menu_message("Request a Call Back")

    return response

def get_vehicle_data_unified(vehicle_name):
    """Get vehicle data from any source (cars, trucks, small trucks)"""
    try:
        vehicle_lower = vehicle_name.lower()

        # Try cars first (Toyota and Toyota)
        sys.path.insert(0, 'cars')

        # Toyota car name mappings only
        Toyota_name_to_key = {
            'camry': 'camry', 'fortuner': 'fortuner', 'fortunur': 'fortuner',  # Include misspelling
            'vellfire': 'vellfire', 'glanza': 'glanza',
            'hyryder': 'hyryder', 'hilux': 'hilux', 'rumion': 'rumion', 'taisor': 'taisor',
            'legender': 'legender', 'innova_crysta': 'innova_crysta', 'innova_hycross': 'innova_hycross',
            'land_cruiser': 'land_cruiser',
        }

        # Check Toyota cars
        if vehicle_lower in Toyota_name_to_key:
            key = Toyota_name_to_key[vehicle_lower]
            return show_car_details_with_buttons.get(key)

        # Check Toyota cars
        if vehicle_lower in Toyota_name_to_key:
            key = Toyota_name_to_key[vehicle_lower]
            return show_car_details_with_buttons.get(key)

    except Exception as e:
        logger.error(f"Error getting vehicle data for {vehicle_name}: {e}")
        return None
class UniversalVehicleManager:
    """Universal Vehicle Manager - Works for ALL vehicle types (cars, trucks, vans, buses, etc.)"""

    def __init__(self):
        self.all_vehicles = {}
        self.vehicle_categories = {}
        self.vehicle_mappings = {}
        self._load_all_vehicles()

    def _load_all_vehicles(self):
        """Load ALL vehicles from ALL sources"""
        try:
            
            actual_car_names = [
                'Camry', 'Fortuner', 'Vellfire','Glanza','Hyryder',
                'Hilux', 'Rumion', 'Taisor', 'Legender',
                'Innova Crysta', 'Innova Hycross', 'Land Cruiser',
            ]

            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name in actual_car_names:  # Only include actual passenger cars
                    vehicle_key = car_name.lower()
                    self.all_vehicles[vehicle_key] = {
                        'name': car_name,
                        'type': 'car',
                        'source': 'CAR_DATA_REGISTRY'
                    }

                    # Add to categories
                    if 'car' not in self.vehicle_categories:
                        self.vehicle_categories['car'] = []
                    self.vehicle_categories['car'].append(car_name)

            # 3. Setup common name mappings (Toyota cars only with misspellings)
            self.vehicle_mappings = {
                'camry': 'Camry',
                'fortuner': 'Fortuner',
                'fortunur': 'Fortuner',  # Common misspelling
                'fortner': 'Fortuner',   # Another misspelling
                'glanza': 'Glanza',
                'hyryder': 'Hyryder',
                'hilux': 'Hilux',
                'rumion': 'Rumion',
                'taisor': 'Taisor',
                'vellfire': 'Vellfire',
                'legender': 'Legender',
                'innova_crysta': 'Innova Crysta',
                'innova crysta': 'Innova Crysta',
                'innova_hycross': 'Innova Hycross',
                'innova hycross': 'Innova Hycross',
                'land_cruiser': 'Land Cruiser',
                'land cruiser': 'Land Cruiser',
            }

            logger.info(f"✅ Universal Vehicle Manager loaded {len(self.all_vehicles)} vehicles")

        except Exception as e:
            logger.error(f"Error loading vehicles: {e}")

    def get_all_vehicles(self, vehicle_type=None):
        """Get all vehicles, optionally filtered by type"""
        if vehicle_type:
            return self.vehicle_categories.get(vehicle_type, [])
        return [info['name'] for info in self.all_vehicles.values()]

    def get_vehicle_by_name(self, name):
        """Get vehicle info by name (with smart matching)"""
        name_lower = name.lower()

        # Direct match
        if name_lower in self.all_vehicles:
            return self.all_vehicles[name_lower]

        # Check mappings
        if name_lower in self.vehicle_mappings:
            mapped_name = self.vehicle_mappings[name_lower]
            return self.get_vehicle_by_name(mapped_name)

        # Partial match
        for vehicle_key, vehicle_info in self.all_vehicles.items():
            if name_lower in vehicle_key or vehicle_key in name_lower:
                return vehicle_info

        return None

    def search_vehicles(self, query, limit=10):
        """Search vehicles by query"""
        query_lower = query.lower()
        results = []

        for vehicle_key, vehicle_info in self.all_vehicles.items():
            if query_lower in vehicle_key or query_lower in vehicle_info['name'].lower():
                results.append(vehicle_info['name'])
                if len(results) >= limit:
                    break

        return results

    def get_vehicles_by_category(self, category):
        """Get vehicles by category (car, truck, small_truck, heavy_truck, etc.)"""
        return self.vehicle_categories.get(category, [])

    def get_vehicle_categories(self):
        """Get all available categories"""
        return list(self.vehicle_categories.keys())
# Global instance
universal_vehicle_manager = UniversalVehicleManager()

def handle_universal_budget_query(prompt, user_id=None):
    """Universal budget handler - works for ALL vehicle types (cars, trucks, vans, buses, etc.)"""
    prompt_lower = prompt.lower()

    # Extract price limit and query type
    import re
    price_limit = None
    query_type = "under"  # default

    # Comprehensive price patterns for all query types
    price_patterns = {
        'under': [
            r'under\s+(\d+)\s*l(?:akh)?s?',
            r'below\s+(\d+)\s*l(?:akh)?s?',
            r'budget\s+(\d+)\s*l(?:akh)?s?',
            r'within\s+(\d+)\s*l(?:akh)?s?',
            r'up\s+to\s+(\d+)\s*l(?:akh)?s?',
            r'maximum\s+(\d+)\s*l(?:akh)?s?',
            r'max\s+(\d+)\s*l(?:akh)?s?',
            r'i\s+have\s+(\d+)l',  # "i have 10l"
            r'my\s+budget\s+is\s+(\d+)\s*l(?:akh)?s?',
            r'budget\s+of\s+(\d+)\s*l(?:akh)?s?'
        ],
        'from': [
            r'from\s+(\d+)\s*l(?:akh)?s?',
            r'starting\s+(\d+)\s*l(?:akh)?s?',
            r'above\s+(\d+)\s*l(?:akh)?s?',
            r'over\s+(\d+)\s*l(?:akh)?s?',
            r'minimum\s+(\d+)\s*l(?:akh)?s?',
            r'min\s+(\d+)\s*l(?:akh)?s?'
        ],
        'range': [
            r'between\s+(\d+)\s*(?:and|to|-)\s*(\d+)\s*l(?:akh)?s?',
            r'(\d+)\s*(?:to|-)\s*(\d+)\s*l(?:akh)?s?'
        ]
    }

    # Try to extract price information
    for query_type_key, patterns in price_patterns.items():
        for pattern in patterns:
            match = re.search(pattern, prompt_lower)
            if match:
                query_type = query_type_key
                if query_type == 'range':
                    price_limit = (int(match.group(1)), int(match.group(2)))
                else:
                    price_limit = int(match.group(1))
                break
        if price_limit:
            break

    if not price_limit:
        return None

    # Determine vehicle type and specific category from query
    vehicle_type = None
    specific_category = None

    vehicle_keywords = {
        'car': ['car', 'cars', 'family car', 'Toyota', 'Toyota'],
        'suv': ['suv', 'suvs'],
        'hatchback': ['hatchback', 'hatchbacks'],
        'sedan': ['sedan', 'sedans'],
        'electric': ['electric', 'ev', 'e-vehicle'],
        'cng': ['cng', 'compressed natural gas'],
        'diesel': ['diesel'],
        'petrol': ['petrol', 'gasoline']
    }

    # Find the most specific vehicle type
    for vtype, keywords in vehicle_keywords.items():
        if any(keyword in prompt_lower for keyword in keywords):
            if vtype in ['suv', 'hatchback', 'sedan']:
                vehicle_type = 'car'
                specific_category = vtype
            else:
                vehicle_type = vtype
            break

    # If no specific type found, default to cars for budget queries
    if not vehicle_type:
        vehicle_type = 'car'

    # Get vehicles based on type
    if vehicle_type in ['car']:
        vehicles = universal_vehicle_manager.get_vehicles_by_category('car')
    elif vehicle_type in ['truck']:
        vehicles = universal_vehicle_manager.get_vehicles_by_category('truck')
    elif vehicle_type in ['electric', 'cng', 'diesel', 'petrol']:
        # For fuel-based queries, first get cars only, then filter by fuel type
        all_cars = universal_vehicle_manager.get_vehicles_by_category('car')
        vehicles = []
        for car_name in all_cars:
            car_data = get_vehicle_data_unified(car_name)
            if car_data:
                fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
                if any(vehicle_type.lower() in ft for ft in fuel_types):
                    vehicles.append(car_name)
        vehicles = vehicles[:15]
    else:
        vehicles = universal_vehicle_manager.get_all_vehicles()

    if not vehicles:
        return None

    # Filter by specific category if specified (SUV, hatchback, sedan)
    if specific_category:
        filtered_vehicles = []
        for vehicle_name in vehicles:
            vehicle_data = get_vehicle_data_unified(vehicle_name)
            if vehicle_data:
                vehicle_category = vehicle_data.get('category', '').lower()
                if specific_category == 'suv' and 'suv' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)
                elif specific_category == 'hatchback' and 'hatchback' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)
                elif specific_category == 'sedan' and 'sedan' in vehicle_category:
                    filtered_vehicles.append(vehicle_name)
            else:
                logger.warning(f"⚠️ No data found for vehicle: {vehicle_name}")
        vehicles = filtered_vehicles

    if not vehicles:
        return None

    # Filter vehicles by budget
    budget_filtered_vehicles = []
    for vehicle_name in vehicles:
        vehicle_data = get_vehicle_data_unified(vehicle_name)
        if vehicle_data:
            # Assuming price is in lakhs and stored as a float/int in vehicle_data
            vehicle_price = vehicle_data.get('price', 0)  # Replace with actual price field
            if query_type == 'under' and vehicle_price <= price_limit:
                budget_filtered_vehicles.append(vehicle_name)
            elif query_type == 'from' and vehicle_price >= price_limit:
                budget_filtered_vehicles.append(vehicle_name)
            elif query_type == 'range' and price_limit[0] <= vehicle_price <= price_limit[1]:
                budget_filtered_vehicles.append(vehicle_name)

    vehicles = budget_filtered_vehicles

    if not vehicles:
        return {
            "message": f"No {specific_category or vehicle_type} found within the specified budget.",
            "buttons": []
        }

    # Generate response based on query type and vehicle type/category
    if vehicle_type == 'car':
        if specific_category == 'suv':
            if query_type == 'under':
                title = f"🚙 **SUVs under ₹{price_limit} Lakh**"
                description = f"Here are excellent SUVs within your budget of ₹{price_limit} Lakh. SUVs offer higher ground clearance, spacious interiors, and commanding road presence:"
            elif query_type == 'from':
                title = f"🚙 **SUVs from ₹{price_limit} Lakh**"
                description = f"Here are premium SUVs starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚙 **SUVs between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are SUVs in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        elif specific_category == 'hatchback':
            if query_type == 'under':
                title = f"🚗 **Hatchbacks under ₹{price_limit} Lakh**"
                description = f"Here are excellent hatchbacks within your budget of ₹{price_limit} Lakh. Hatchbacks are compact, fuel-efficient, and perfect for city driving:"
            elif query_type == 'from':
                title = f"🚗 **Hatchbacks from ₹{price_limit} Lakh**"
                description = f"Here are premium hatchbacks starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Hatchbacks between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are hatchbacks in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        elif specific_category == 'sedan':
            if query_type == 'under':
                title = f"🚗 **Sedans under ₹{price_limit} Lakh**"
                description = f"Here are excellent sedans within your budget of ₹{price_limit} Lakh. Sedans offer spacious interiors, comfort, and elegant styling:"
            elif query_type == 'from':
                title = f"🚗 **Sedans from ₹{price_limit} Lakh**"
                description = f"Here are premium sedans starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Sedans between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are sedans in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
        else:
            if query_type == 'under':
                title = f"🚗 **Cars under ₹{price_limit} Lakh**"
                description = f"Here are excellent cars within your budget of ₹{price_limit} Lakh:"
            elif query_type == 'from':
                title = f"🚗 **Cars from ₹{price_limit} Lakh**"
                description = f"Here are premium cars starting from ₹{price_limit} Lakh:"
            else:  # range
                title = f"🚗 **Cars between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
                description = f"Here are cars in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
    elif vehicle_type == 'truck':
        if query_type == 'under':
            title = f"🚛 **Trucks under ₹{price_limit} Lakh**"
            description = f"Here are commercial vehicles within your budget of ₹{price_limit} Lakh:"
        elif query_type == 'from':
            title = f"🚛 **Trucks from ₹{price_limit} Lakh**"
            description = f"Here are heavy-duty trucks starting from ₹{price_limit} Lakh:"
        else:  # range
            title = f"🚛 **Trucks between ₹{price_limit[0]}-{price_limit[1]} Lakh**"
            description = f"Here are trucks in your budget range of ₹{price_limit[0]}-{price_limit[1]} Lakh:"
    else:
        title = f"🚗 **{vehicle_type.title()} vehicles in your budget**"
        description = f"Here are {vehicle_type} vehicles matching your budget criteria:"

    # Limit vehicles for display
    display_vehicles = vehicles[:10]

    message = f"{title}\n\n{description}\n\n"

    # Add vehicle preview
    for i, vehicle in enumerate(display_vehicles[:5], 1):
        message += f"{i}. {vehicle}\n"

    if len(vehicles) > 5:
        message += f"\n📋 Total: {len(vehicles)} models available in your budget\n"

    message += "\n*Which vehicle would you like to explore?*"

    return {
        "message": message,
        "buttons": [{
            "data": display_vehicles,
            "data_type": "list",
            "message": "📋 Here are some vehicles you can explore. Please select one:"
        }]
    }

def handle_universal_vehicle_query(prompt, user_id=None):
    """Universal vehicle query handler - replaces all separate vehicle-type handlers"""
    prompt_lower = prompt.lower()

    # Define query patterns and their corresponding vehicle categories
    query_patterns = {
        # Car queries
        'family car': 'car',
        'family': 'car',
        'Toyota cars': 'car',
        'Toyota cars': 'car',
        'car': 'car',
        'hatchback': 'car',
        'sedan': 'car',
        'suv': 'car',
        'hybrid': 'car',
        'hybrids': 'car',


        # Fuel-based queries
        'electric': None,  # Will search across all types
        'cng': None,
        'diesel': None,
        'petrol': None
    }

    # Find matching pattern with priority (fuel-based queries first, then longer matches)
    matched_pattern = None
    matched_category = None

    # First, check for fuel-based queries (highest priority)
    fuel_patterns = ['electric', 'cng', 'diesel', 'petrol']
    for fuel in fuel_patterns:
        if fuel in prompt_lower:
            matched_pattern = fuel
            matched_category = None  # Fuel-based queries have no specific category

            break

    # If no fuel pattern found, check other patterns (longer matches first)
    if not matched_pattern:
        sorted_patterns = sorted(query_patterns.items(), key=lambda x: len(x[0]), reverse=True)
        for pattern, category in sorted_patterns:
            if pattern in prompt_lower and pattern not in fuel_patterns:
                matched_pattern = pattern
                matched_category = category
                break

    if matched_pattern:
        # Get vehicles based on category
        if matched_category:
            vehicles = universal_vehicle_manager.get_vehicles_by_category(matched_category)
        else:
            # For fuel-based queries, filter cars by fuel type (not trucks)
            if matched_pattern in ['electric', 'cng', 'diesel', 'petrol']:
                all_cars = universal_vehicle_manager.get_vehicles_by_category('car')
                vehicles = []
                for car_name in all_cars:
                    car_data = get_vehicle_data_unified(car_name)
                    if car_data:
                        fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
                        if any(matched_pattern.lower() in ft for ft in fuel_types):
                            vehicles.append(car_name)
                vehicles = vehicles[:15]  # Limit results

                # If no cars found with this fuel type, return appropriate message
                if not vehicles:
                    fuel_type_name = matched_pattern.title()
                    # Special handling for hybrid cars - show all cars instead of error
                    if matched_pattern.lower() == 'hybrid':
                        logger.info(f"🔍 No hybrid cars found, showing all available cars instead")
                        all_cars = universal_vehicle_manager.get_vehicles_by_category('car')
                        if all_cars:
                            return {
                                "message": f"🚗 **Hybrid Cars**\n\n❌ **Pure Hybrid Models Currently Not Available**\n\nWe don't have dedicated hybrid cars in our current Toyota lineup. However, we have excellent fuel-efficient petrol and CNG options:\n\n📋 **Available Cars:**\n" + "\n".join([f"{i+1}. {car}" for i, car in enumerate(all_cars[:10])]) + f"\n\n📋 Total: {len(all_cars)} models available\n\n💡 **Suggestion:** Consider our CNG cars for eco-friendly driving or petrol cars with excellent mileage.\n\n*Which car would you like to explore?*",
                                "buttons": [{
                                    "data": all_cars[:10],
                                    "data_type": "list",
                                    "message": "📋 Here are our available cars. Please select one:"
                                }]
                            }
                    return {
                        "message": f"🚗 **{fuel_type_name} Cars**\n\n❌ **Currently Not Available**\n\nWe don't have {fuel_type_name.lower()} cars in our current Toyota lineup.\n\n✅ **Available Options:**\n• Petrol Cars - Most popular choice\n• CNG Cars - Eco-friendly & economical\n\n💡 **Suggestion:** Try asking for 'petrol cars' or 'CNG cars' to see available options.\n\n📞 **Need Help?** Contact us for more information about our current inventory.",
                        "buttons": []
                    }
            else:
                # For other queries, search across all vehicles
                vehicles = universal_vehicle_manager.search_vehicles(matched_pattern, limit=15)

        if vehicles:
            # Generate appropriate message
            if matched_pattern in ['family car', 'family']:
                message_title = "👨‍👩‍👧‍👦 **Perfect family cars for you!**"
                message_desc = "Spacious, safe, and comfortable vehicles designed for family journeys."
            elif matched_pattern == 'electric':
                message_title = "⚡ **Go green with electric vehicles!**"
                message_desc = "Zero emissions, low running costs—perfect for modern needs."
            elif matched_pattern == 'cng':
                message_title = "🌿 **Explore CNG-powered vehicles!**"
                message_desc = "Cleaner and more cost-effective for day-to-day use."
            else:
                message_title = f"🚗 **{matched_pattern.title()} vehicles for you!**"
                message_desc = "Explore our range of vehicles designed for your specific needs."

            message = f"{message_title}\n{message_desc}\n\n"

            # Add vehicle list preview
            vehicle_preview = vehicles[:5]  # Show first 5
            for i, vehicle in enumerate(vehicle_preview, 1):
                message += f"{i}. {vehicle}\n"

            if len(vehicles) > 5:
                message += f"\n📋 Total: {len(vehicles)} models available\n"

            message += "\n*Which vehicle interests you?*"

            return {
                "message": message,
                "buttons": [{
                    "data": vehicles[:10],  # Limit to 10 for WhatsApp compatibility
                    "data_type": "list",
                    "message": "📋 Here are some vehicles you can explore. Please select one:"

                }]
            }

    return None
def get_whatsapp_response(step_name, user_id=None):
    """
    Get response using new car data system with media_id support - NO NAMESPACING

    Args:
        step_name (str): The step name to get response for (car name or action)
        user_id (str): User ID for context setting

    Returns:
        dict: Formatted response with message, data, and media_id if available
    """
    try:
        # Handle special main menu actions that require user_id capture
        if step_name in ["Request a Call Back", "Book a Test Drive", "Talk With Live Agent", "Chat With Bot"] and user_id:
            # Get user's car context (last viewed car) or fallback to 'Not specified'
            car_context = get_user_car_context(user_id)

            # Save user_id to appropriate Google Sheet
            if step_name == "Request a Call Back":
                handle_test_drive_callback_request('call_back', user_id, car_context)
                logger.info(f"✅ Call back request saved for user {user_id} from main menu")
            elif step_name == "Book a Test Drive":
                handle_test_drive_callback_request('test_drive', user_id, car_context)
                logger.info(f"✅ Test drive request saved for user {user_id} from main menu")
            elif step_name == "Talk With Live Agent":
                # Parse user ID for database operations
                actual_user_id, session_id_parsed, project_id = parse_user_id(user_id)

                # Update handover status to enable live agent
                logger.info(f"🔄 Initiating live agent handover for user {user_id}")
                handover_result = update_handover("bhandari", actual_user_id, True)

                if "error" not in handover_result:
                    # Create live chat session in database (non-blocking)
                    import threading

                    def create_session_background():
                        try:
                            session_token = sync_create_live_chat_session(actual_user_id, session_id_parsed)
                            if session_token:
                                logger.info(f"✅ Live chat session created for user {user_id}: {session_token}")
                                # Log the interaction
                                sync_insert_user_interaction(actual_user_id, session_id_parsed, "live_chat_request",
                                                            {"session_token": session_token, "car_context": car_context, "handover_status": True})
                            else:
                                logger.warning(f"❌ Failed to create live chat session for user {user_id}")
                        except Exception as e:
                            logger.warning(f"Background live chat session creation failed: {e}")

                    # Start session creation in background thread
                    session_thread = threading.Thread(target=create_session_background, daemon=True)
                    session_thread.start()

                    # Return success message immediately (don't wait for DB operations)
                    return {
                        "message": "🤝 **Agent Handover Completed**\n\nYou are now connected to a live agent who will assist you with your automotive needs.\n\nPlease wait a moment for an agent to respond.",
                        "data": [],
                        "data_type": "message"
                    }
                else:
                    logger.error(f"❌ Handover failed for user {user_id}: {handover_result}")
                    return {
                        "message": "❌ **Unable to Connect to Live Agent**\n\nWe're experiencing technical difficulties. Please try again later or contact us directly.",
                        "data": [],
                        "data_type": "message"
                    }
            elif step_name == "Chat With Bot":
                # Parse user ID for database operations
                actual_user_id, session_id_parsed, project_id = parse_user_id(user_id)
                # Log bot chat interaction
                sync_insert_user_interaction(actual_user_id, session_id_parsed, "bot_chat_request",
                                           {"car_context": car_context})
                logger.info(f"✅ Bot chat interaction logged for user {user_id}")

        # First, check if it's a main menu entry (prioritize main menu over car data)
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle main menu entries
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for entry in response_data:
                        if "message" in entry:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += entry["message"]

                        if "data" in entry:
                            combined_buttons.append({
                                "data": entry["data"],
                                "data_type": entry.get("data_type", "list"),
                                "message": entry.get("message", "")
                            })

                        if "Media_ID" in entry and not media_id:
                            media_id = entry["Media_ID"]

                        # Use the data_type from the first entry that has it
                        if "data_type" in entry:
                            primary_data_type = entry["data_type"]

                    # Fix for WhatsApp: Set the first button's message to the combined message
                    # to avoid duplication when WhatsApp reads the button message
                    if combined_buttons:
                        combined_buttons[0]["message"] = combined_message

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "text")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }]
                    }

                    if media_id:
                        response["media_id"] = media_id

                    return response

        # Then, try to get car data directly (no namespacing) - but skip Main Menu
        if step_name != "Main Menu":
            car_response = get_car_response_with_media_id(step_name, user_id=user_id)
            if car_response:
                # Set user context for car-specific responses
                if user_id:
                    set_user_context(user_id, step_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {step_name}")
                return car_response

        # Check if it's a car action - but prioritize user context if available
        current_car = None
        if user_id:
            current_car = get_user_context(user_id)

        # If user has a current car context, check that car first
        if current_car:
            car_data = get_car_data_by_name(current_car)
            if car_data and step_name in car_data:
                # IMPORTANT: Ensure context is maintained for button clicks
                logger.info(f"🎯 Using car context for button click: {step_name} -> {current_car}")
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    # Handle multi-item sections (Exterior, Interior, Gallery)
                    if len(response_data) > 1 and step_name in ['Exterior', 'Interior', 'Gallery']:
                        # For gallery sections, avoid duplicating the "click below" message
                        first_item = response_data[0]
                        second_item = response_data[1] if len(response_data) > 1 else {}

                        # Get data from first item (with images)
                        first_message = first_item.get("message", "")
                        first_data = first_item.get("data", [])
                        first_data_type = first_item.get("data_type", "image")
                        media_id = first_item.get("Media_ID", "")

                        # Get data from second item (with buttons)
                        second_data = second_item.get("data", [])
                        second_data_type = second_item.get("data_type", "button")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": current_car,
                            "message": first_message,
                            "data": first_data,
                            "data_type": first_data_type,
                            "buttons": [{
                                "data": first_data,
                                "data_type": first_data_type,
                                "message": first_message
                            }, {
                                "data": second_data,
                                "data_type": second_data_type,
                                "message": "👇For more options click below 🔗✨"
                            }],
                            "hasButtons": len(second_data) > 0
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id
                    else:
                        # Handle single item or non-gallery sections
                        step_config = response_data[0]
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        media_id = step_config.get("Media_ID", "")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": current_car,
                            "message": message,
                            "buttons": [{
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            }],
                            "hasButtons": len(data) > 0,
                            "data_type": data_type
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id

                    logger.info(f"✅ Context-based action match: {current_car} -> {step_name}")
                    return response

        # If no context or context didn't match, check all cars (but this should be rare now)
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name == 'Main Menu':
                continue

            car_data = CAR_DATA_REGISTRY[car_name]
            if step_name in car_data:
                response_data = car_data[step_name]
                if isinstance(response_data, list) and len(response_data) > 0:
                    # Handle multi-item sections (Exterior, Interior, Gallery)
                    if len(response_data) > 1 and step_name in ['Exterior', 'Interior', 'Gallery']:
                        # For gallery sections, avoid duplicating the "click below" message
                        first_item = response_data[0]
                        second_item = response_data[1] if len(response_data) > 1 else {}

                        # Get data from first item (with images)
                        first_message = first_item.get("message", "")
                        first_data = first_item.get("data", [])
                        first_data_type = first_item.get("data_type", "image")
                        media_id = first_item.get("Media_ID", "")

                        # Get data from second item (with buttons)
                        second_data = second_item.get("data", [])
                        second_data_type = second_item.get("data_type", "button")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": car_name,
                            "message": first_message,
                            "data": first_data,
                            "data_type": first_data_type,
                            "buttons": [{
                                "data": first_data,
                                "data_type": first_data_type,
                                "message": first_message
                            }, {
                                "data": second_data,
                                "data_type": second_data_type,
                                "message": "👇For more options click below 🔗✨"
                            }],
                            "hasButtons": len(second_data) > 0
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id
                    else:
                        # Handle single item or non-gallery sections
                        step_config = response_data[0]
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        media_id = step_config.get("Media_ID", "")

                        response = {
                            "status": "success",
                            "step": step_name,
                            "car_name": car_name,
                            "message": message,
                            "buttons": [{
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            }],
                            "hasButtons": len(data) > 0,
                            "data_type": data_type
                        }

                        # Add media_id if present
                        if media_id:
                            response["media_id"] = media_id
                            response["buttons"][0]["media_id"] = media_id

                    # Set user context
                    if user_id:
                        set_user_context(user_id, car_name)
                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")

                    logger.info(f"✅ General action match: {car_name} -> {step_name}")
                    return response

        # Fallback to main menu or WHATSAPP_CONFIG
        if step_name in MAIN_MENU_DATA:
            response_data = MAIN_MENU_DATA[step_name]
            if isinstance(response_data, list) and len(response_data) > 0:
                # Handle multiple entries in main menu
                if len(response_data) > 1:
                    # Combine multiple entries into a single response
                    combined_message = ""
                    combined_buttons = []
                    primary_data_type = "button"  # Default to button
                    media_id = ""

                    for i, step_config in enumerate(response_data):
                        message = step_config.get("message", "")
                        data = step_config.get("data", [])
                        data_type = step_config.get("data_type", "list")
                        config_media_id = step_config.get("Media_ID", "")

                        # Combine messages with line breaks
                        if message:
                            if combined_message:
                                combined_message += "\n\n"
                            combined_message += message

                        # Create separate button entries for each step
                        if data:
                            combined_buttons.append({
                                "data": data,
                                "data_type": data_type,
                                "message": message
                            })

                            # Add media_id if present
                            if config_media_id:
                                combined_buttons[-1]["media_id"] = config_media_id
                                if not media_id:  # Use first media_id found
                                    media_id = config_media_id

                        # Use the first data_type as primary
                        if i == 0:
                            primary_data_type = data_type

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": combined_message,
                        "buttons": combined_buttons,
                        "hasButtons": len(combined_buttons) > 0,
                        "data_type": primary_data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id

                    return response
                else:
                    # Single entry - original logic
                    step_config = response_data[0]
                    message = step_config.get("message", "")
                    data = step_config.get("data", [])
                    data_type = step_config.get("data_type", "list")
                    media_id = step_config.get("Media_ID", "")

                    response = {
                        "status": "success",
                        "step": step_name,
                        "message": message,
                        "buttons": [{
                            "data": data,
                            "data_type": data_type,
                            "message": message
                        }],
                        "hasButtons": len(data) > 0,
                        "data_type": data_type
                    }

                    # Add media_id if present
                    if media_id:
                        response["media_id"] = media_id
                        response["buttons"][0]["media_id"] = media_id

                    return response

        # Final fallback to old WHATSAPP_CONFIG (for backward compatibility)
        if step_name in WHATSAPP_CONFIG:
            step_data = WHATSAPP_CONFIG[step_name]
            if isinstance(step_data, list) and len(step_data) > 0:
                step_config = step_data[0]
                message = step_config.get("message", "")
                data = step_config.get("data", [])
                data_type = step_config.get("data_type", "list")
                media_id = step_config.get("Media_ID", "")

                response = {
                    "status": "success",
                    "step": step_name,
                    "message": message,
                    "buttons": [{
                        "data": data,
                        "data_type": data_type,
                        "message": message
                    }],
                    "hasButtons": len(data) > 0,
                    "data_type": data_type
                }

                # Add media_id if present
                if media_id:
                    response["media_id"] = media_id
                    response["buttons"][0]["media_id"] = media_id

                return response

        # Not found
        return {
            "status": "error",
            "message": f"Step '{step_name}' not found in any configuration",
            "data": None,
            "data_type": "text"
        }

    except Exception as e:
        logger.error(f"Error in get_whatsapp_response: {str(e)}")
        return {
            "status": "error",
            "message": f"Error getting WhatsApp response: {str(e)}"
        }

def set_user_context(session_id, car_name):
    """Set the current car context for a user session"""
    if session_id:
        USER_CONTEXT[session_id] = {
            'current_car': car_name,
            'timestamp': time.time()
        }

def get_user_context(session_id):
    """Get the current car context for a user session"""
    if session_id and session_id in USER_CONTEXT:
        return USER_CONTEXT[session_id].get('current_car')
    return None

def show_car_details_with_buttons(car_name, session_id=None):
    """
    Show car details with action buttons in WhatsApp-friendly format
    Prioritizes JSON flow over car data manager and sets user context
    """
    # Set user context when they select a car
    if session_id:
        set_user_context(session_id, car_name)

    # First check if car exists in JSON config (exact match)
    if car_name in WHATSAPP_CONFIG:
        return get_whatsapp_response(car_name)

    # Check for case-insensitive match in JSON config
    for key in WHATSAPP_CONFIG.keys():
        if key.lower() == car_name.lower():
            if session_id:
                set_user_context(session_id, key)  # Use the exact key as context
            return get_whatsapp_response(key)

    # Check for partial matches in JSON config (for car names)
    car_name_lower = car_name.lower()
    for key in WHATSAPP_CONFIG.keys():
        if car_name_lower in key.lower() or key.lower() in car_name_lower:
            # Additional check to ensure it's likely a car name, not a generic action
            if any(car_word in key.lower() for car_word in ['camry', 'fortuner', 'glanza', 'hyryder', 'hilux', 'rumion', 'taisor', 'vellfire', 'legender', 'innova', 'land']):
                if session_id:
                    set_user_context(session_id, key)  # Use the exact key as context
                return get_whatsapp_response(key)

    # Get car data from car data manager as fallback
    car_data = get_car_data(car_name)

    if car_data:
        # Use car data specifications
        return format_car_details_from_data(car_data, {}, {})

    # If not found, return error
    return {
        "status": "error",
        "message": f"❌ Sorry, '{car_name}' not found in our inventory"
    }
def post_process_ai_criteria(criteria, query):
    """
    Post-process AI-extracted criteria to make them more user-friendly
    """
    query_lower = query.lower()
    price_range = criteria.get("price_range", {})

    # Handle "starting from" queries - add reasonable upper bound
    if (price_range.get("min") and not price_range.get("max") and
        ('from starting' in query_lower or 'starting from' in query_lower or
         ('starting' in query_lower and 'lakh' in query_lower))):

        base_price = price_range["min"]
        # Set reasonable upper bound (2.5x the starting price)
        criteria["price_range"]["max"] = base_price * 2.5
        logger.info(f"Added upper bound for 'starting from' query: {base_price} -> {base_price * 2.5}")

    return criteria

def extract_search_criteria_with_ai(query):
    """
    Use AI to extract search criteria from natural language query
    """


        # Create a focused prompt for search criteria extraction
    extraction_prompt = f"""
Analyze this car search query and extract the search criteria in JSON format.

Query: "{query}"

Extract the following criteria if mentioned:
- car_names: List of specific car names mentioned
- fuel_types: List of fuel types (petrol, diesel, cng, hybrid, electric)
- price_range: {{min: number, max: number}} in lakhs (convert any price mentions)
- categories: List of categories (hatchback, sedan, suv, mpv, premium, budget, entry-level)
- features: List of specific features mentioned
- transmission: List of transmission types (manual, automatic, amt, cvt)
- dealership: Toyota or Toyota if specifically mentioned

Return only valid JSON format:
{{
  "car_names": [],
  "fuel_types": [],
  "price_range": {{"min": null, "max": null}},
  "categories": [],
  "features": [],
  "transmission": [],
  "dealership": null,
  "general_intent": "brief description of what user wants"
}}
"""

        # Use a simple chat session for extraction
    chat = model.start_chat()
    response = chat.send_message(extraction_prompt)

    if response.candidates and response.candidates[0].content.parts:
            ai_response = response.candidates[0].content.parts[0].text

            # Try to parse JSON from AI response
            import json
            try:
                # Extract JSON from response (in case AI adds extra text)
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = ai_response[json_start:json_end]
                    criteria = json.loads(json_str)
                    # Post-process AI results for better user experience
                    criteria = post_process_ai_criteria(criteria, query)
                    return criteria
            except json.JSONDecodeError:
                logger.warning(f"Could not parse AI response as JSON: {ai_response}")
    else:
        logger.warning(f"AI response is empty or invalid: {response}")

    return {}

def car_matches_criteria(car_data, criteria, category):
    """
    Check if a car matches the extracted search criteria
    """
    car_name = car_data.get('name', 'Unknown')
    car_category = car_data.get('category', '').lower()

    # Debug logging
    logger.debug(f"🔍 Checking car: {car_name} (category: '{car_category}') against criteria: {criteria}")

    # If specific car names are mentioned, check for exact match
    if criteria.get("car_names"):
        car_name_lower = car_name.lower()
        for search_name in criteria["car_names"]:
            if search_name.lower() in car_name_lower or car_name_lower in search_name.lower():
                logger.debug(f"✅ Car name match: {car_name}")
                return True
        logger.debug(f"❌ Car name no match: {car_name}")
        return False  # If specific names mentioned but no match, exclude

    # Check categories (SUV, hatchback, sedan, etc.) - STRICT FILTERING
    if criteria.get("categories"):
        category_match = False
        for search_category in criteria["categories"]:
            search_cat_lower = search_category.lower()
            logger.debug(f"🔍 Checking category '{car_category}' against search category '{search_cat_lower}'")

            # Handle SUV matching (includes Compact SUV, Mid-size SUV, Micro SUV)
            if search_cat_lower == 'suv' and 'suv' in car_category:
                logger.debug(f"✅ SUV category match: {car_name} ({car_category})")
                category_match = True
                break
            # Handle hatchback matching
            elif search_cat_lower == 'hatchback' and 'hatchback' in car_category:
                logger.debug(f"✅ Hatchback category match: {car_name} ({car_category})")
                category_match = True
                break
            # Handle sedan matching
            elif search_cat_lower == 'sedan' and 'sedan' in car_category:
                logger.debug(f"✅ Sedan category match: {car_name} ({car_category})")
                category_match = True
                break
            # Handle Hybrid matching
            elif search_cat_lower == 'hybrid' and 'hybrid' in car_category:
                logger.debug(f"✅ Hybrid category match: {car_name} ({car_category})")
                category_match = True
                break
            # Generic category matching
            elif search_cat_lower in car_category or car_category in search_cat_lower:
                logger.debug(f"✅ Generic category match: {car_name} ({car_category})")
                category_match = True
                break

        if not category_match:
            logger.debug(f"❌ No category match for {car_name} ({car_category})")
            return False  # No category match found - STRICT filtering

    # Check fuel types
    if criteria.get("fuel_types"):
        car_fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
        for search_fuel in criteria["fuel_types"]:
            search_fuel_lower = search_fuel.lower()
            # Handle hybrid matching - since Toyota doesn't have pure hybrids, 
            # treat hybrid queries as general car queries (don't filter out)
            if search_fuel_lower == 'hybrid':
                logger.debug(f"🔍 Hybrid query detected, including all cars: {car_name}")
                break  # Include all cars for hybrid queries
            elif search_fuel_lower == 'petrol':
                if any('petrol' in ft for ft in car_fuel_types):
                    break
            elif any(search_fuel_lower in ft for ft in car_fuel_types):
                break
        else:
            # Only exclude if it's not a hybrid query
            if not any(sf.lower() == 'hybrid' for sf in criteria["fuel_types"]):
                return False  # No fuel type match found

    # Check price range
    price_range = criteria.get("price_range", {})
    if price_range.get("min") or price_range.get("max"):
        # Try to find variants in the new data structure
        variants = car_data.get('variants', [])

        # If no variants found, try the new format with car name + " variants"
        if not variants:
            car_name = car_data.get('name', '')
            # Try different variant key formats
            for key in car_data.keys():
                if key.endswith(' variants'):
                    variants = car_data[key]
                    break

        price_match = False
        for variant in variants:
            # Handle both 'price' and 'price_range' fields
            price_str = variant.get('price', '') or variant.get('price_range', '')
            price_numbers = re.findall(r'[\d.]+', price_str)
            if price_numbers:
                car_price = float(price_numbers[0])
                min_price = price_range.get("min")
                max_price = price_range.get("max")

                # Check if car price matches the criteria
                matches_min = not min_price or car_price >= min_price
                matches_max = not max_price or car_price <= max_price

                if matches_min and matches_max:
                    price_match = True
                    break

        if not price_match:
            return False

    # Check dealership preference
    if criteria.get("dealership"):
        if criteria["dealership"].lower() != category.lower():
            return False

    # If no specific criteria matched but no exclusions either, include the car
    return True

    if not category_match:
            logger.debug(f"❌ No category match for {car_name} ({car_category})")
            return False  # No category match found - STRICT filtering

    # Check fuel types
    if criteria.get("fuel_types"):
        car_fuel_types = [ft.lower() for ft in car_data.get('fuel_types', [])]
        for search_fuel in criteria["fuel_types"]:
            search_fuel_lower = search_fuel.lower()
            # Handle hybrid matching - since Maruti doesn't have pure hybrids, 
            # treat hybrid queries as general car queries (don't filter out)
            if search_fuel_lower == 'hybrid':
                logger.debug(f"🔍 Hybrid query detected, including all cars: {car_name}")
                break  # Include all cars for hybrid queries
            elif search_fuel_lower == 'petrol':
                if any('petrol' in ft for ft in car_fuel_types):
                    break
            elif any(search_fuel_lower in ft for ft in car_fuel_types):
                break
        else:
            # Only exclude if it's not a hybrid query
            if not any(sf.lower() == 'hybrid' for sf in criteria["fuel_types"]):
                return False  # No fuel type match found

    # Check price range
    price_range = criteria.get("price_range", {})
    if price_range.get("min") or price_range.get("max"):
        # Try to find variants in the new data structure
        variants = car_data.get('variants', [])

        # If no variants found, try the new format with car name + " variants"
        if not variants:
            car_name = car_data.get('name', '')
            # Try different variant key formats
            for key in car_data.keys():
                if key.endswith(' variants'):
                    variants = car_data[key]
                    break

        price_match = False
        for variant in variants:
            # Handle both 'price' and 'price_range' fields
            price_str = variant.get('price', '') or variant.get('price_range', '')
            price_numbers = re.findall(r'[\d.]+', price_str)
            if price_numbers:
                car_price = float(price_numbers[0])
                min_price = price_range.get("min")
                max_price = price_range.get("max")

                # Check if car price matches the criteria
                matches_min = not min_price or car_price >= min_price
                matches_max = not max_price or car_price <= max_price

                if matches_min and matches_max:
                    price_match = True
                    break

        if not price_match:
            return False

    # Check dealership preference
    if criteria.get("dealership"):
        if criteria["dealership"].lower() != category.lower():
            return False

    # If no specific criteria matched but no exclusions either, include the car
    return True


def search_cars(query):
    """
    Intelligent car search using AI to understand natural language queries
    Handles both cars and trucks based on query intent
    """
    # Use AI to understand the search intent and extract criteria
    search_criteria = extract_search_criteria_with_ai(query)

    # Debug logging to understand what criteria are being extracted
    logger.info(f"🔍 Search criteria extracted for '{query}': {search_criteria}")

    # Get all available cars
    all_cached_cars = car_data_manager.get_all_cars()
    matching_cars = []

    # Search through all cars using AI-extracted criteria
    for category in ['Toyota', 'Toyota']:
        for _, car_data in all_cached_cars.get(category, {}).items():
            if car_matches_criteria(car_data, search_criteria, category):
                enhanced_car = {
                    'name': car_data.get('name', ''),
                    'category': car_data.get('category', ''),
                    'dealership_type': category.title(),
                    'fuel_types': car_data.get('fuel_types', []),
                    'specifications': car_data,
                    'dealership_info': {}
                }
                matching_cars.append(enhanced_car)
                logger.info(f"✅ Car matched: {car_data.get('name')} (category: {car_data.get('category')})")
            else:
                logger.debug(f"❌ Car not matched: {car_data.get('name')} (category: {car_data.get('category')})")

    logger.info(f"🔍 Total matching cars found: {len(matching_cars)} for query '{query}'")

    return {
        "status": "success",
        "cars": matching_cars,
        "total_found": len(matching_cars),
        "query": query,
        "search_type": "ai_powered_search",
        "criteria": search_criteria
    }

def get_all_cars():
    """
    Get all cars from cars_data.py and the car data manager
    """
    all_cars = []

    # Add cars from cars_data.py as the primary source
    if CARS_DATA:
        for car_key, car_data in CARS_DATA.items():
            # Make a deep copy to avoid modifying the original data
            car_copy = car_data.copy()
            # Ensure required fields are present
            car_copy['name'] = car_data.get('name', car_key.replace('_', ' ').title())
            car_copy['dealership_type'] = car_data.get('dealership_type', 'Toyota')
            car_copy['category'] = car_data.get('category', 'Car')
            car_copy['fuel_types'] = car_data.get('fuel_types', ['Petrol'])
            # Add car to list
            all_cars.append(car_copy)
            
    # If no cars in CARS_DATA, try car data manager
    if not all_cars:
        try:
            all_cached_cars = car_data_manager.get_all_cars()
            if isinstance(all_cached_cars, dict):
                for car_key, car_data in all_cached_cars.items():
                    if isinstance(car_data, dict):
                        car_copy = car_data.copy()
                        car_copy['name'] = car_data.get('name', car_key.replace('_', ' ').title())
                        car_copy['dealership_type'] = car_data.get('dealership_type', 'Toyota')
                        car_copy['category'] = car_data.get('category', 'Car')
                        car_copy['fuel_types'] = car_data.get('fuel_types', ['Petrol'])
                        all_cars.append(car_copy)
                        
        except Exception as e:
            logger.warning(f"⚠️ car_data_manager.get_all_cars() failed: {e}")

    # Final fallback: If still no cars, use CAR_DATA_REGISTRY
    if not all_cars and CAR_DATA_REGISTRY:
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name != 'Main Menu':
                all_cars.append({
                    'name': car_name,
                    'dealership_type': 'Toyota',
                    'category': 'Car',
                    'fuel_types': ['Petrol']
                })

    # Apply filtering and limiting to match utility.py behavior
    filtered_cars = []
    for car in all_cars:
        name = car.get('name', '')
        # Remove "Urban Cruiser" prefix
        clean_name = name.replace("Urban Cruiser ", "")
        car_copy = car.copy()
        car_copy['name'] = clean_name
        filtered_cars.append(car_copy)
    
    # Limit to 10 cars
    filtered_cars = filtered_cars[:10]

    logger.info(f"🚗 get_all_cars() returning {len(filtered_cars)} cars (filtered from {len(all_cars)})")
    
    # Return formatted response for API consistency
    return {
        "status": "success",
        "cars": filtered_cars,
        "total_found": len(filtered_cars),
        "total_count": len(filtered_cars)
    }

def find_car_matches(query, threshold=0.6):
    """
    Find and extract the best matching car names from a query using fuzzy and exact matching.

    Args:
        query (str): The user query containing potential car names
        threshold (float): Minimum similarity threshold for fuzzy matching (0.0 to 1.0)

    Returns:
        list: List of matched car names, or empty list if no matches found
    """
    if not query:
        return []

    query_lower = query.lower().strip()
    all_car_names = get_all_car_names()
    matched_cars = []

    # Noise words to skip
    noise_words = {
        # Articles & pronouns
        'i', 'me', 'my', 'mine', 'we', 'our', 'ours', 
        'you', 'your', 'yours', 'he', 'she', 'they', 'them', 'it',
        'a', 'an', 'the',

        # Intent / filler
        'want', 'looking', 'searching', 'need', 'buy', 'purchase', 'show',
        'find', 'give', 'tell', 'suggest', 'recommend', 'please',

        # Prepositions
        'to', 'for', 'about', 'on', 'in', 'at', 'by', 'of', 'from', 'with', 'into',

        # Connectors
        'and', 'or', 'vs', 'v/s', 'vs.', 'versus', 
        'compare', 'comapre', 'compar', 'campare',

        # Extra noise words
        'regarding', 'related', 'info', 'information', 'details', 'detail',
        'cost', 'price', 'prices', 'rate', 'rates', 'value',
        'model', 'models', 'version', 'variant', 'variants'
    }

    # Helper function to find a single car name match
    def find_single_car_match(name):
        name_lower = name.lower().strip()

        # Check direct mappings first
        if name_lower in CAR_NAME_MAPPINGS:
            return CAR_NAME_MAPPINGS[name_lower]

        # Check universal vehicle manager mappings (includes misspellings)
        if name_lower in universal_vehicle_manager.vehicle_mappings:
            return universal_vehicle_manager.vehicle_mappings[name_lower]

        # Check exact matches in vehicle registry
        if name_lower in all_car_names:
            return name_lower

        # Try fuzzy matching with lower threshold for better misspelling detection
        matches = difflib.get_close_matches(name_lower, all_car_names, n=1, cutoff=threshold)
        if matches:
            closest_match = matches[0]
            logger.info(f"🔤 Fuzzy match: '{name}' -> '{closest_match}'")

            # Convert back to canonical name
            if closest_match in CAR_NAME_MAPPINGS:
                return CAR_NAME_MAPPINGS[closest_match]

            # Find in CAR_DATA_REGISTRY
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name.lower() == closest_match:
                    return car_name

            return closest_match

        # Try fuzzy matching against vehicle mappings keys (for misspellings)
        mapping_keys = list(universal_vehicle_manager.vehicle_mappings.keys())
        fuzzy_mapping_matches = difflib.get_close_matches(name_lower, mapping_keys, n=1, cutoff=threshold)
        if fuzzy_mapping_matches:
            matched_key = fuzzy_mapping_matches[0]
            canonical_name = universal_vehicle_manager.vehicle_mappings[matched_key]
            logger.info(f"🔤 Fuzzy mapping match: '{name}' -> '{matched_key}' -> '{canonical_name}'")
            return canonical_name

        # Try partial matches
        for car_name_in_registry in CAR_DATA_REGISTRY.keys():
            if name_lower in car_name_in_registry.lower():
                return car_name_in_registry

        # Check alternative name mappings for partial matches
        for alt_name, canonical_name in CAR_NAME_MAPPINGS.items():
            if name_lower in alt_name.lower() or alt_name.lower() in name_lower:
                return canonical_name

        return None

    # Split query into words and check each for car names
    words = query_lower.split()
    for word in words:
        if word in noise_words:
            continue

        closest_match = find_single_car_match(word)
        if closest_match and closest_match not in matched_cars:
            matched_cars.append(closest_match)

    # Try multi-word combinations (two words)
    for i in range(len(words) - 1):
        two_word = f"{words[i]} {words[i+1]}"
        if all(w in noise_words for w in two_word.split()):
            continue

        closest_match = find_single_car_match(two_word)
        if closest_match and closest_match not in matched_cars:
            matched_cars.append(closest_match)

    return matched_cars
def get_vehicle_variants_unified(vehicle_data):
    """Get variants from vehicle data in a unified format"""
    try:
        # Check for car variants (multiple possible keys)
        for key in vehicle_data.keys():
            if 'variants' in key:
                return vehicle_data[key]

        # For trucks, variants might not be explicitly defined
        # Return empty list if no variants found
        return []

    except Exception as e:
        logger.error(f"Error getting vehicle variants: {e}")
        return []

def generate_universal_comparison_buttons(prompt):
    """
    Extracts two vehicles for comparison and returns buttons with exact names used for comparison.
    Handles cars, trucks, vans, buses, fuzzy matches, and alternative mappings.
    Only triggers when the user explicitly asks for a comparison.
    """
    prompt_lower = prompt.lower()

    # 🚨 Guard clause: run only for comparison prompts
    comparison_keywords = ["compare", "vs", "versus", "difference between"]
    if not any(keyword in prompt_lower for keyword in comparison_keywords):
        return []  # Skip if user is asking about Interior/Exterior/etc.

    # 1. Collect all vehicle names from all sources
    all_vehicles = {}

    # Cars
    for car_name in CAR_DATA_REGISTRY.keys():
        all_vehicles[car_name.lower()] = car_name
    # Name mappings (including common misspellings) - Toyota cars only
    vehicle_name_mappings = {
        'camry': 'Camry',
        'fortuner': 'Fortuner',
        'fortunur': 'Fortuner',  # Common misspelling
        'fortner': 'Fortuner',   # Another misspelling
        'glanza': 'Glanza',
        'hyryder': 'Hyryder',
        'hilux': 'Hilux',
        'rumion': 'Rumion',
        'taisor': 'Taisor',
        'vellfire': 'Vellfire',
        'legender': 'Legender',
        'innova_crysta': 'Innova Crysta',
        'innova crysta': 'Innova Crysta',
        'innova_hycross': 'Innova Hycross',
        'innova hycross': 'Innova Hycross',
        'land_cruiser': 'Land Cruiser',
        'land cruiser': 'Land Cruiser',
    }

    # 2. Extract vehicles using patterns
    import re
    comparison_patterns = [
        r'compare\s+([a-zA-Z0-9\s\-\.]+?)\s+(?:vs|and|with)\s+([a-zA-Z0-9\s\-\.]+?)',
        r'([a-zA-Z0-9\s\-\.]+?)\s+vs\s+([a-zA-Z0-9\s\-\.]+?)',
        r'([a-zA-Z0-9\s\-\.]+?)\s+versus\s+([a-zA-Z0-9\s\-\.]+?)',
        r'difference\s+between\s+([a-zA-Z0-9\s\-\.]+?)\s+and\s+([a-zA-Z0-9\s\-\.]+?)'
    ]

    extracted_vehicles = []
    for pattern in comparison_patterns:
        matches = re.findall(pattern, prompt_lower, re.IGNORECASE)
        for match in matches:
            vehicle1_query, vehicle2_query = match[0].strip(), match[1].strip()
            vehicle1_matches = find_car_matches(vehicle1_query)
            vehicle2_matches = find_car_matches(vehicle2_query)
            if vehicle1_matches and vehicle2_matches:
                extracted_vehicles = [vehicle1_matches[0], vehicle2_matches[0]]
                break
        if extracted_vehicles:
            break

    # 3. Fallback using fuzzy matching
    if not extracted_vehicles:
        try:
            fuzzy_matches = find_car_matches(prompt)
            for fm in fuzzy_matches:
                fm_key = fm.lower().strip()
                if fm_key in all_vehicles:
                    candidate = all_vehicles[fm_key]
                elif fm_key in vehicle_name_mappings:
                    candidate = vehicle_name_mappings[fm_key]
                else:
                    candidate = fm.title()
                if candidate not in extracted_vehicles:
                    extracted_vehicles.append(candidate)
                if len(extracted_vehicles) >= 2:
                    break
        except Exception as e:
            logger.warning(f"Fuzzy matching failed: {e}")
            pass
    
    # 4. Additional fallback: try to extract individual words and match them
    if len(extracted_vehicles) < 2:
        words = prompt_lower.split()
        for word in words:
            if word in ['compare', 'vs', 'versus', 'and', 'with']:
                continue
            
            # Check if word matches any vehicle name or mapping
            if word in all_vehicles:
                candidate = all_vehicles[word]
                if candidate not in extracted_vehicles:
                    extracted_vehicles.append(candidate)
            elif word in vehicle_name_mappings:
                candidate = vehicle_name_mappings[word]
                if candidate not in extracted_vehicles:
                    extracted_vehicles.append(candidate)
            
            if len(extracted_vehicles) >= 2:
                break

    # 5. Only return buttons if exactly 2 vehicles found
    if len(extracted_vehicles) >= 2:
        vehicle1, vehicle2 = extracted_vehicles[:2]
        logger.info(f"✅ Comparison vehicles extracted: {vehicle1} vs {vehicle2}")
        return [{
            "data": [vehicle1, vehicle2],
            "data_type": "button",
            "message": f"📋 Compare {vehicle1} vs {vehicle2} - select one for full details:"
        }]
    
    logger.info(f"❌ Could not extract 2 vehicles for comparison from: '{prompt}'. Found: {extracted_vehicles}")
    return []




def generate_comparison_buttons(prompt):
    """Legacy function - now calls the universal comparison function"""
    return generate_universal_comparison_buttons(prompt)


def check_whatsapp_flow_match(prompt, user_id=None):
    """
    Check if the prompt matches any WhatsApp flow step using NEW CAR DATA SYSTEM
    Prioritizes exact matches and car-specific actions

    Args:
        prompt (str): User input prompt
        user_id (str): User ID for context lookup

    Returns:
        dict or None: WhatsApp response if match found, None otherwise
    """
    prompt_clean = prompt.strip()
    prompt_lower = prompt_clean.lower()

    # PRIORITY 0: Check for exact WhatsApp flow button matches FIRST
    # This ensures button clicks always go to WhatsApp flows, not LLM
    if prompt_clean in MAIN_MENU_DATA:
        logger.info(f"✅ PRIORITY WhatsApp flow button match: '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # Case-insensitive main menu matches
    for key in MAIN_MENU_DATA.keys():
        if key.lower() == prompt_clean.lower():
            logger.info(f"✅ PRIORITY WhatsApp flow button match (case-insensitive): '{prompt}' -> {key}")
            return get_whatsapp_response(key, user_id)

    # PRIORITY 0.1: Direct car name match (high priority for button clicks)
    car_response = get_car_response_with_media_id(prompt_clean, user_id=user_id)
    if car_response:
        proper_car_name = car_response.get('car_name', prompt_clean)
        logger.info(f"✅ PRIORITY Direct car match: '{prompt}' -> {proper_car_name}")
        # Set user context for direct car matches using proper car name
        if user_id:
            set_user_context(user_id, proper_car_name)
            logger.info(f"🎯 Set user context: {user_id} -> {proper_car_name}")
        return car_response

    # PRIORITY 0.2: Handle generic "car" queries - don't let them match "Tata Commercials"
    # These should go to the AI functions instead
    generic_car_queries = [
        'car', 'cars', 'show me cars', 'i want cars', 'looking for cars',
        'i want to buy a car', 'show cars', 'all cars', 'available cars',
        'what cars do you have', 'car models', 'car options'
    ]
    if prompt_lower.strip() in generic_car_queries:
        logger.info(f"🚗 Generic car query detected: '{prompt}' - routing to AI functions")
        return None  # Let AI functions handle this

    # PRIORITY 0.3: Handle service-related queries - route to WhatsApp flows
    def matches_service_query(prompt):
        service_patterns = [
            'i want to book a service', 'i need a service', 'book a service',
            'service booking', 'book service', 'schedule service', 'service appointment',
            'car service', 'vehicle service', 'maintenance', 'repair',
            'i want service', 'need service', 'book my service', 'i want to service',
            'need to service', 'want to book service'
        ]
        # Check for exact matches first, then partial matches
        prompt_lower = prompt.lower().strip()

        # Direct matches
        if prompt_lower in service_patterns:
            return True

        # Partial matches for longer phrases
        for pattern in service_patterns:
            if pattern in prompt_lower and len(prompt_lower) <= len(pattern) + 10:
                return True

        return False

    def matches_test_drive_query(prompt):
        test_drive_patterns = [
            'i want to book a test drive', 'book a test drive', 'test drive',
            'i want test drive', 'book test drive', 'schedule test drive',
            'i want to test drive', 'can i test drive', 'test drive booking'
        ]
        return any(pattern in prompt.lower() for pattern in test_drive_patterns)

    def matches_call_back_query(prompt):
        call_back_patterns = [
            'request a call back', 'call back', 'call me back', 'callback',
            'i want a call back', 'request call back', 'please call me',
            'can you call me', 'i need a call back'
        ]
        return any(pattern in prompt.lower() for pattern in call_back_patterns)

    # Check for service-related queries first
    if matches_service_query(prompt_clean):
        logger.info(f"🔧 Service query detected: '{prompt}' - routing to Book a Service flow")
        base_response = get_whatsapp_response("Book a Service", user_id)
        if base_response:
            return base_response
        return None

    if matches_test_drive_query(prompt_clean):
        logger.info(f"🚗 Test drive query detected: '{prompt}' - routing to Book a Test Drive flow")
        base_response = get_whatsapp_response("Book a Test Drive", user_id)
        if base_response:
            return base_response
        return None

    if matches_call_back_query(prompt_clean):
        logger.info(f"📞 Call back query detected: '{prompt}' - routing to Request a Call Back flow")
        base_response = get_whatsapp_response("Request a Call Back", user_id)
        if base_response:
            return base_response
        return None

    # PRIORITY 3: Check for car actions using new system - CONTEXT FIRST
    # First, check if user has a current car context and the action exists in that car
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            car_data = get_car_data_by_name(current_car)

            # Try exact match first
            if car_data and prompt_clean in car_data:
                response = get_car_response_with_media_id(current_car, prompt_clean, user_id)
                if response:
                    logger.info(f"✅ Context-based car action match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return response

            # Try smart action mapping for common actions
            if car_data:
                action_mappings = {
                    'Ex-showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    '🚗Ex‑showroom Price': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'Ex-Showroom Prices': ['🚗Ex‑showroom Price', 'Ex-showroom Price', 'Ex‑showroom Price', 'Ex-Showroom Prices'],
                    'More about this car': ['More about this', 'More about this car'],
                    'Request Brochure': ['Request Brochure', 'Request for Brochure', 'Brochure','Request a Brochure'],
                    'Book Test Drive': ['Book Test Drive', 'Book a Test Drive']
                }

                if prompt_clean in action_mappings:
                    for mapped_action in action_mappings[prompt_clean]:
                        if mapped_action in car_data:
                            response = get_car_response_with_media_id(current_car, mapped_action, user_id)
                            if response:
                                logger.info(f"✅ Context-based smart action match: '{prompt}' -> {current_car} -> {mapped_action}")
                                return response

    # If no context or context didn't match, check all cars
    for car_name in CAR_DATA_REGISTRY.keys():
        if car_name == 'Main Menu':
            continue

        car_data = CAR_DATA_REGISTRY[car_name]

        # Check if prompt matches any action in this car's data
        if prompt_clean in car_data:
            # Use get_car_response_with_media_id with specific car and action
            response = get_car_response_with_media_id(car_name, prompt_clean, user_id)
            if response:
                logger.info(f"✅ Car action match: '{prompt}' -> {car_name} -> {prompt_clean}")
                # Set user context
                if user_id:
                    set_user_context(user_id, car_name)
                    logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                return response

    # PRIORITY 3: Handle comparison requests using universal comparison system
    if any(phrase in prompt.lower() for phrase in [
        'both variants', 'both prices', 'both variant', 'show me both',
        'variants of both', 'comaprision','comparison','comapre',
        'compare', 'vs', 'versus', 'difference between', 'diffrence between'
    ]):
        logger.info(f"🔍 Universal comparison request detected: '{prompt}'")

        # Try to extract car names using find_car_matches
        car_matches = find_car_matches(prompt)
        logger.info(f"🚗 Found car matches: {car_matches}")

        if len(car_matches) >= 2:
            # Use the first two cars found for comparison
            car1, car2 = car_matches[:2]
            logger.info(f"🔍 Comparing: {car1} vs {car2}")

            # Use unified_car_system to make the comparison
            try:
                from unified_car_system import compare_cars
                comparison_result = compare_cars(car1, car2)

                if comparison_result["status"] == "success":
                    # Create buttons for both cars
                    comparison_buttons = [{
                        "data": [car1, car2],
                        "data_type": "list",
                        "message": comparison_result["comparison"]
                    }]

                    return {
                        "message": comparison_result["comparison"],
                        "data": [car1, car2],
                        "data_type": "list",
                        "buttons": comparison_buttons
                    }
                else:
                    logger.warning(f"❌ Comparison failed: {comparison_result.get('message', 'Unknown error')}")
            except Exception as e:
                logger.error(f"❌ Error during comparison: {str(e)}")

        # Fallback if we could not extract 2 vehicles or comparison failed
        return {
            "message": (
                "🔍 **Car Comparison**\n\n"
                "Please specify which cars you'd like to compare. For example:\n"
                "• 'Compare Fortuner vs Camry'\n"
                "• 'Glanza vs Hyryder variants'\n"
                "• 'Show me both Camry and Fortuner'"
            ),
            "data": [],
            "data_type": "text",
            "buttons": []
        }

    # PRIORITY 4: Check for exact matches in WHATSAPP_CONFIG
    if prompt_clean in WHATSAPP_CONFIG:
        logger.info(f"✅ WhatsApp config match: '{prompt}' -> {prompt_clean}")
        return get_whatsapp_response(prompt_clean, user_id)

    # PRIORITY 5: Context-based actions using new system (only if no specific car mentioned)
    if user_id:
        current_car = get_user_context(user_id)
        if current_car:
            # Check if a different car is mentioned in the prompt
            different_car_mentioned = False
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name != current_car and car_name.lower() in prompt_lower:
                    different_car_mentioned = True
                    break

            # Also líderescheck name mappings for different cars
            if not different_car_mentioned:
                for mapping, car_name in CAR_NAME_MAPPINGS.items():
                    if car_name != current_car and mapping in prompt_lower:
                        different_car_mentioned = True
                        break

            # Only use context if no different car is mentioned
            if not different_car_mentioned:
                # Try to find action in current car's data
                car_data = get_car_data_by_name(current_car)
                if car_data and prompt_clean in car_data:
                    logger.info(f"✅ Context-based match: '{prompt}' -> {current_car} -> {prompt_clean}")
                    return get_whatsapp_response(prompt_clean, user_id)

                # Try common action patterns with smart mapping
                if any(word in prompt_lower for word in ['price', 'variant', 'ex-showroom', 'showroom']):
                    # Use unified price key detection
                    price_key = get_price_key_for_car(car_data)
                    if price_key:
                        logger.info(f"✅ Context-based price match: '{prompt}' -> {current_car} -> {price_key}")
                        return get_whatsapp_response(price_key, user_id)

  
    # Handle variant/price requests with unified approach
    variant_patterns = ['variants', 'variant', 'variantes', 'price', 'prices', 'ex-showroom', 'showroom']
    if any(pattern in prompt_lower for pattern in variant_patterns):
        logger.info(f"🔍 Variant/price request detected: '{prompt}'")

        # Find which car is mentioned
        detected_car = None

        # Check direct car names first
        for car_name in CAR_DATA_REGISTRY.keys():
            if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                detected_car = car_name
                break

        # Check name mappings if no direct match
        if not detected_car:
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    detected_car = car_name
                    break

        # If we found a car, try to get its variants/prices
        if detected_car:
            logger.info(f"🚗 Detected car for variant request: {detected_car}")
            car_data = get_car_data_by_name(detected_car)

            if car_data:
                # Use unified price key detection
                price_key = get_price_key_for_car(car_data)
                if price_key:
                    logger.info(f"✅ Found price key for {detected_car}: {price_key}")
                    response = get_car_response_with_media_id(detected_car, price_key, user_id)
                    if response:
                        # Set user context
                        if user_id:
                            set_user_context(user_id, detected_car)
                            logger.info(f"🎯 Set user context: {user_id} -> {detected_car}")
                        return response

    # Handle other patterns
    other_patterns = {
        'more about': ['More about', 'More about this'],
        'tell me about': ['More about', 'More about this'],
        'show me': ['More about', 'More about this'],
        'details of': ['More about', 'More about this'],
        'information about': ['More about', 'More about this'],
        'brochure': ['Brochure', 'Request Brochure', 'Request for Brochure'],
        'test drive': ['Book Test Drive', 'Book a Test Drive']
    }

    for pattern, action_variations in other_patterns.items():
        if pattern in prompt_lower:
            # Find which car is mentioned
            for car_name in CAR_DATA_REGISTRY.keys():
                if car_name != 'Main Menu' and car_name.lower() in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"{action_suffix} {car_name}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action, user_id)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

            # Also check name mappings
            for mapping, car_name in CAR_NAME_MAPPINGS.items():
                if mapping in prompt_lower:
                    car_data = get_car_data_by_name(car_name)

                    # Try all action variations for this pattern
                    for action_suffix in action_variations:
                        possible_actions = [
                            f"{car_name} {action_suffix}",
                            f"More about {car_name}",
                            action_suffix
                        ]

                        for action in possible_actions:
                            if action in car_data:
                                logger.info(f"✅ Mapping pattern match: '{prompt}' -> {car_name} -> {action}")
                                # Use specific car context instead of general search
                                response = get_car_response_with_media_id(car_name, action, user_id)
                                if response:
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_name)
                                        logger.info(f"🎯 Set user context: {user_id} -> {car_name}")
                                    return response
                    break

    # PRIORITY 7: Check if this should go to LLM (information queries)
    info_keywords = [
        'information', 'info', 'details', 'detail', 'address', 'contact',
        'phone', 'number', 'location', 'where', 'how to', 'tell me about',
        'dealer', 'dealership', 'showroom address', 'showroom contact',
        'working hours', 'timings', 'directions', 'reach'
    ]

    if any(keyword in prompt_lower for keyword in info_keywords):
        logger.info(f"🤖 Routing to LLM: '{prompt}' (detected info query)")
        return None

    return None

def normalize_field_name(field_name):
    """
    Normalize field names for consistent access

    Args:
        field_name (str): Original field name from form

    Returns:
        str: Normalized field name
    """
    if not field_name:
        return field_name

    # Convert to lowercase and replace spaces with underscores
    normalized = field_name.lower().replace(' ', '_').replace('-', '_')

    # Handle specific field name mappings
    field_mappings = {
        # Alternate number variations
        'alternate_number': 'alternate_number',
        'alternative_number': 'alternate_number',
        'alt_number': 'alternate_number',
        'alternate_phone': 'alternate_number',
        'alternative_phone': 'alternate_number',
        'second_number': 'alternate_number',
        'secondary_number': 'alternate_number',

        # Mobile number variations (be careful with 'phone' - handle separately)
        'mobile_number': 'mobile_number',
        'phone_number': 'mobile_number',
        'mobile': 'mobile_number',
        'primary_phone': 'mobile_number',
        'primary_number': 'mobile_number',

        # Vehicle number variations
        'vehicle_number': 'vehicle_number',
        'registration_number': 'vehicle_number',
        'reg_number': 'vehicle_number',
        'car_number': 'vehicle_number',
        'vehicle_reg': 'vehicle_number',

        # Name variations
        'full_name': 'name',
        'customer_name': 'name',
        'user_name': 'name',
        'client_name': 'name'
    }

    return field_mappings.get(normalized, normalized)

def identify_form_type(form_data):
    """
    Identify the type of Meta form based on form data

    Args:
        form_data (dict): Form data from Meta webhook

    Returns:
        str: Form type identifier
    """
    # First check if form_name is directly provided (most reliable)
    form_name = form_data.get('form_name', '').lower()
    logger.info(f"🔍 Form identification - form_name: '{form_name}'")

    if 'user_car_buy' in form_name:
        logger.info(f"✅ Form identified by name: buy_used_car_form")
        return 'buy_used_car_form'
    elif 'user_car_sell' in form_name:
        logger.info(f"✅ Form identified by name: sell_used_car_form")
        return 'sell_used_car_form'
    elif 'accessories' in form_name:
        logger.info(f"✅ Form identified by name: purchase_accessories_form")
        return 'purchase_accessories_form'
    elif 'book_a_service' in form_name or 'book a service' in form_name:
        logger.info(f"✅ Form identified by name: service_form")
        return 'service_form'
    elif 'book_a_insurance' in form_name:
        logger.info(f"✅ Form identified by name: insurance_form")
        return 'insurance_form'

    # Check for buy used car form indicators
    buy_used_car_indicators = [
        'buy_used_cars', 'user_car_buy', 'buy_pre_owned', 'buy_used_car',
        'price_range', 'budget_range', 'looking_for_car', 'want_to_buy'
    ]

    # Check for sell used car form indicators
    sell_used_car_indicators = [
        'sell_used_car', 'user_car_sell', 'sell_your_car', 'sell_car',
        'vehicle_model', 'year_of_manufacture', 'manufacturing_year'
    ]

    # Check for purchase accessories form indicators
    purchase_accessories_indicators = [
        'purchase_accessories', 'accessories', 'buy_accessories', 'accessory',
        'item_required', 'car_accessories', 'vehicle_accessories'
    ]

    # Check for insurance form indicators
    insurance_indicators = [
        'insurance', 'book_insurance', 'Book_a_Insurance', 'book_a_insurance',
        'policy', 'premium', 'coverage', 'claim', 'motor_insurance',
        'vehicle_insurance', 'car_insurance', 'insurance_type'
    ]

    # Check for service form indicators
    service_indicators = [
        'service', 'book_service', 'Book_a_Service', 'book_a_service', 'maintenance', 'repair',
        'servicing', 'car_service', 'vehicle_service', 'service_booking',
        'service_appointment', 'oil_change', 'brake_service', 'tune_up', 'service_type'
    ]

    # Convert form data to lowercase string for checking
    form_str = json.dumps(form_data).lower()

    # Check for service form FIRST (most common and has overlapping fields)
    if any(indicator.lower() in form_str for indicator in service_indicators):
        return 'service_form'

    # Check for purchase accessories form

    # Check specific field names for service FIRST (highest priority)
    service_fields = ['service_type', 'service_date', 'car_registration', 'vehicle_number']
    if any(field in form_data for field in service_fields):
        # Additional check: if vehicle_number is present, make sure it's not a sell car form
        # by checking for sell-specific fields
        if 'vehicle_number' in form_data:
            sell_specific_fields = ['vehicle_model', 'year_of_manufacture', 'manufacturing_year']
            if any(field in form_data for field in sell_specific_fields):
                # This might be a sell car form, continue with other checks
                pass
            else:
                # vehicle_number without sell-specific fields = service form
                logger.info(f"✅ Form identified by fields: service_form (found: {[f for f in service_fields if f in form_data]})")
                return 'service_form'
        else:
            # Other service fields found
            logger.info(f"✅ Form identified by fields: service_form (found: {[f for f in service_fields if f in form_data]})")
            return 'service_form'

    # Check specific field names for buy used car (be more specific)
    buy_car_fields = ['price_range', 'budget_range', 'preferred_model']
    if any(field in form_data for field in buy_car_fields) and \
       not any(field in form_data for field in ['service_type', 'vehicle_number']):
        logger.info(f"✅ Form identified by fields: buy_used_car_form (found: {[f for f in buy_car_fields if f in form_data]})")
        return 'buy_used_car_form'

    # Check specific field names for sell used car
    sell_car_fields = ['vehicle_model', 'vehicle_number', 'year_of_manufacture']
    if any(field in form_data for field in sell_car_fields) and \
       not any(field in form_data for field in ['service_type']):
        logger.info(f"✅ Form identified by fields: sell_used_car_form (found: {[f for f in sell_car_fields if f in form_data]})")
        return 'sell_used_car_form'


    # Default to service form
    logger.info(f"⚠️ Form type defaulted to: service_form (no specific indicators found)")
    return 'service_form'
def format_price_range(price_range):
    """
    Format price range from various formats to user-friendly display

    Args:
        price_range: Can be string like "3_5_LAKH", list, or other format

    Returns:
        str: Formatted price range like "3-5 lakhs" or "3 to 5 lakhs"
    """
    if not price_range or price_range == 'N/A':
        return 'N/A'

    try:
        # Convert to string if it's a list
        if isinstance(price_range, list):
            price_range = ', '.join(str(item) for item in price_range)

        price_str = str(price_range)

        # Handle formats like "3_5_LAKH", "3-5_LAKH", etc.
        if '_' in price_str and 'LAKH' in price_str.upper():
            # Extract numbers from formats like "3_5_LAKH"
            numbers = price_str.replace('_LAKH', '').replace('_lakh', '').replace('_', '-')
            if '-' in numbers:
                return f"{numbers} lakhs"
            else:
                return f"{numbers} lakhs"

        # Handle formats like "3-5", "3 to 5", etc.
        elif '-' in price_str or 'to' in price_str.lower():
            # Already in good format, just add "lakhs" if not present
            if 'lakh' not in price_str.lower():
                return f"{price_str} lakhs"
            return price_str

        # Handle single numbers
        elif price_str.replace('.', '').isdigit():
            return f"{price_str} lakhs"

        # Return as-is if already formatted
        return price_str

    except Exception as e:
        logger.warning(f"Error formatting price range '{price_range}': {e}")
        return str(price_range)
class generaterequest(BaseModel):
    prompt: str
    user_id: str
    liveagent_status: Optional[int] = 0
@app.post('/generate')
async def generate_text(request: generaterequest):
    global SESSION_ID, model

    # Track request start time for performance logging
    request_start_time = time.time()

    # ✅ Helper to detect greetings
    def is_greeting(prompt):
        # Only treat simple 'hi' or 'hello' as greeting triggers for main menu
        greetings = {"hello", "hi"}
        return prompt.strip().lower() in greetings

    response_data = {
        "text": "",
        "function_response_id": "",
        "functin_response": [],
        "llm": ""
    }

    # Flag to track if function calls were processed
    function_calls_processed = False
    
    # Initialize token counts to avoid scope issues
    query_token_count = 0
    llm_token_count = 0

    try:

        prompt = request.prompt
        user_id = request.user_id
        liveagent_status = request.liveagent_status


        if not prompt or not user_id:
            return JSONResponse(content={'error': 'Missing prompt or user_id in request body'}, status_code=400)

        prompt = prompt.strip()
        function_response = ""

        # Parse user ID for database logging
        actual_user_id, session_id, project_id = parse_user_id(user_id)

        # Log the incoming user message (we'll log response later with token counts)
        try:
            # We'll log with token counts after getting the response
            pass
        except Exception as db_error:
            logger.warning(f"Database logging failed: {db_error}")
            # Continue processing even if DB logging fails

        # Initialize session if needed
        if user_id not in SESSION_ID:
            SESSION_ID[user_id] = []

        # ✅ Main menu trigger: greeting only (not new user)
        if is_greeting(prompt):
            SESSION_ID[user_id] = []  # reset session
            # Main menu key in main_menu.json is "Main Menu"
            greeting_response = get_whatsapp_response("Main Menu")
            response_data["text"] = greeting_response["message"]
            response_data["llm"] = greeting_response["message"]
            response_data["function_response_id"] = 1
            response_data["functin_response"] = greeting_response.get("buttons", [])

            # Add media_id if present in greeting response
            if "media_id" in greeting_response:
                response_data["media_id"] = greeting_response["media_id"]

        # Check for car listing requests
        elif any(phrase in prompt.lower() for phrase in ["all cars", "show cars", "list cars", "available cars", "what cars"]):
            logger.info("🚗 Direct get_all_cars() call for car listing request")
            result = get_all_cars()
            
            if result["status"] == "success" and result["cars"]:
                # Use the utility function to format the response properly
                formatted_response = format_all_cars_message(result)
                
                response_data["text"] = formatted_response["generated_text"]
                response_data["llm"] = formatted_response["generated_text"]
                response_data["function_response_id"] = formatted_response["function_response_id"]
                response_data["functin_response"] = formatted_response["function_response"]
                logger.info("✅ Successfully handled cars query")
            else:
                response_data["text"] = "❌ No cars available at the moment"
                response_data["llm"] = response_data["text"]
                response_data["function_response_id"] = 1
                response_data["functin_response"] = []
        else:
            logger.info(f"🔍 Processing non-greeting prompt: '{prompt}'")

                # First check if this is a comparison request
            def detect_comparison_intent(text):
                """Helper to detect if text is asking for a comparison"""
                comparison_phrases = [
                    'both variants', 'both prices', 'both variant', 'show me both',
                    'variants of both', 'comaprision', 'comparison', 'comapre', 'compare',
                    'vs', 'versus', 'difference between', 'diffrence between', ' and '
                ]
                return any(phrase in text.lower() for phrase in comparison_phrases)

            if detect_comparison_intent(prompt):
                logger.info(f"🔍 Universal comparison request detected: '{prompt}'")

                    # Try to extract car names using find_car_matches
                car_matches = find_car_matches(prompt)
                if len(car_matches) >= 2:
                    # Use the first two cars found for comparison
                    car1, car2 = car_matches[:2]
                    logger.info(f"🔍 Comparing: {car1} vs {car2}")

                    if len(car_matches) >= 2:
                        # Use the first two cars found for comparison
                        car1, car2 = car_matches[:2]
                        logger.info(f"🔍 Comparing: {car1} vs {car2}")

                        # Use unified_car_system to make the comparison
                        try:
                            from unified_car_system import compare_cars
                            comparison_result = compare_cars(car1, car2)

                            if comparison_result["status"] == "success":
                                # Create buttons for both cars
                                comparison_buttons = [{
                                    "data": [car1, car2],
                                    "data_type": "list",
                                    "message": comparison_result["comparison"]
                                }]

                                response_data = {
                                    "generated_text": comparison_result["comparison"],
                                    "function_response_id": 1,
                                    "function_response": comparison_buttons
                                }
                                logger.info(f"✅ Successfully generated comparison between {car1} and {car2}")
                                return JSONResponse(content=response_data)
                            else:
                                logger.warning(f"❌ Comparison failed: {comparison_result.get('message', 'Unknown error')}")
                        except Exception as e:
                            logger.error(f"❌ Error during comparison: {str(e)}")

                    # Fallback if we could not extract 2 vehicles or comparison failed
                    fallback_response = {
                        "generated_text": (
                            "🔍 **Car Comparison**\n\n"
                            "Please specify which cars you'd like to compare. For example:\n"
                            "• 'Compare Fortuner vs Camry'\n"
                            "• 'Glanza vs Hyryder variants'\n"
                            "• 'Show me both Camry and Fortuner'"
                        ),
                        "function_response_id": 1,
                        "function_response": []
                    }
                    return JSONResponse(content=fallback_response)

                # If not a comparison, proceed with regular car detection
                try:
                    prompt_key = prompt.strip()
                    car_match = None

                    # First check for exact car name matches
                    for cname in CAR_DATA_REGISTRY.keys():
                        if cname != 'Main Menu' and cname.lower() == prompt_key.lower():
                            car_match = cname
                            break

                    # Check name mappings for exact matches
                    if not car_match:
                        mapped = prompt_key.lower()
                        if mapped in CAR_NAME_MAPPINGS:
                            car_match = CAR_NAME_MAPPINGS[mapped]

                    # Check for car names mentioned in phrases like "show me glanza car"
                    if not car_match:
                        prompt_lower = prompt.lower()
                        # Check if any car name is mentioned in the prompt
                        for cname in CAR_DATA_REGISTRY.keys():
                            if cname != 'Main Menu' and cname.lower() in prompt_lower:
                                # Make sure it's not just a substring match
                                words = prompt_lower.split()
                                if cname.lower() in words or any(cname.lower() in word for word in words):
                                    car_match = cname
                                    break                    # Also check name mappings in phrases
                    if not car_match:
                        for mapped_name, canonical_name in CAR_NAME_MAPPINGS.items():
                            if mapped_name in prompt_lower:
                                words = prompt_lower.split()
                                if mapped_name in words or any(mapped_name in word for word in words):
                                    car_match = canonical_name
                                    break

                    if car_match:
                        logger.info(f" Car name detected: '{prompt}' -> '{car_match}' - returning car flow")
                        car_response = get_car_response_with_media_id(car_match, user_id=user_id)
                        if not car_response:
                            # Fallback to show_car_details_with_buttons
                            result = show_car_details_with_buttons(car_match, session_id=user_id)
                            message = result.get('message', '') if isinstance(result, dict) else ''
                            buttons = result.get('buttons', []) if isinstance(result, dict) else []
                            final = {
                                'generated_text': message,
                                'function_response_id': 1,
                                'function_response': buttons
                            }
                            if isinstance(result, dict) and 'media_id' in result:
                                final['media_id'] = result['media_id']
                            return JSONResponse(content=final)
                        else:
                            resp_text = car_response.get('message', '')
                            resp_buttons = car_response.get('buttons', [])
                            final = {
                                'generated_text': resp_text,
                                'function_response_id': 1,
                                'function_response': resp_buttons
                            }
                            if 'media_id' in car_response:
                                final['media_id'] = car_response['media_id']
                            # Set user context for this exact car view
                            if user_id:
                                set_user_context(user_id, car_match)
                                update_user_car_context(user_id, car_match)
                            return JSONResponse(content=final)
                except Exception as _:
                    # If early detection fails, continue with regular processing
                    logger.debug("Early car-name detection failed, continuing regular flow")

                # Check for form submission (JSON data with flow_token or form fields)
                try:
                    import json
                    raw_form_data = json.loads(prompt)
                    if isinstance(raw_form_data, dict) and ('flow_token' in raw_form_data or
                        any(field in raw_form_data for field in ['name', 'mobile_number', 'email', 'service_type'])):
                        logger.info(f"🔍 Detected form submission: {raw_form_data}")

                        # Normalize form data field names for consistent processing
                        form_data = {}

                        # First pass: copy all original data
                        for key, value in raw_form_data.items():
                            form_data[key] = value

                        # Special handling for phone/mobile_number fields
                        if 'phone' in raw_form_data and 'mobile_number' in raw_form_data:
                            # Both phone and mobile_number exist - use phone as alternate
                            form_data['alternate_number'] = raw_form_data['phone']
                            logger.info(f"🔍 Using phone {raw_form_data['phone']} as alternate number")
                        elif 'phone' in raw_form_data and 'mobile_number' not in raw_form_data:
                            # Only phone exists - use it as mobile_number
                            form_data['mobile_number'] = raw_form_data['phone']
                            logger.info(f"🔍 Using phone {raw_form_data['phone']} as mobile number")

                        # Second pass: add other normalized keys
                        for key, value in raw_form_data.items():
                            if key == 'phone':
                                continue  # Already handled above

                            normalized_key = normalize_field_name(key)

                            # Only set normalized key if it doesn't already exist or if the original key is the same
                            if normalized_key not in form_data or key == normalized_key:
                                form_data[normalized_key] = value

                        logger.info(f"🔍 Normalized form data: {form_data}")

                        # Process form submission
                        form_type = identify_form_type(form_data)
                        logger.info(f"🔍 Form type identified: {form_type}")

                        # Store in Google Sheets
                        success = store_meta_form_data(form_data, form_type)

            if success:
                # Generate appropriate thank you message based on form type
                if form_type == 'buy_used_car_form':
                    price_range = form_data.get('price_range', 'N/A')
                    # Format price range properly
                    formatted_price_range = format_price_range(price_range)
                    thank_you_message = f"✅ **Buy Used Car Request Submitted!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Email: {form_data.get('email', 'N/A').replace('mailto:', '')}\n• Price Range: {formatted_price_range}\n• Location: {form_data.get('location', 'N/A')}\n\n🚗 **What's Next:**\n• Our sales team will contact you within 24 hours\n• We'll show you the best used cars in your budget\n• Complete documentation and financing assistance\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing us for your used car purchase!"
                elif form_type == 'sell_used_car_form':
                    thank_you_message = f"✅ **Sell Used Car Request Submitted!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Email: {form_data.get('email', 'N/A').replace('mailto:', '')}\n• Vehicle Model: {form_data.get('vehicle_model', 'N/A')}\n• Vehicle Number: {form_data.get('vehicle_number', 'N/A')}\n• Year of Manufacture: {form_data.get('year_of_manufacture', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n💰 **What's Next:**\n• Our evaluation team will contact you within 24 hours\n• We'll schedule a vehicle inspection\n• Get the best price for your vehicle\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing us to sell your vehicle!"
                elif form_type == 'purchase_accessories_form':
                    thank_you_message = f"✅ **Purchase Accessories Request Submitted!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Email: {form_data.get('email', 'N/A').replace('mailto:', '')}\n• Item Required: {form_data.get('item_required', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n🛒 **What's Next:**\n• Our accessories team will contact you within 24 hours\n• We'll provide you with product details and pricing\n• Installation and delivery services available\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing our accessories!"
                elif form_type == 'insurance_form':
                    thank_you_message = f"✅ **Insurance Application Submitted!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Alternate No: {form_data.get('alternate_number', 'N/A')}\n• Vehicle No: {form_data.get('vehicle_number', 'N/A')}\n• Insurance Type: {form_data.get('insurance_type', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n🛡️ **What's Next:**\n• Our insurance team will contact you within 24 hours\n• We'll provide you with the best insurance quotes\n• Policy will be processed as per your requirements\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing our insurance services!"
                else:
                           # Service form (default)
                    thank_you_message = f"✅ **Service Booking Confirmed!**\n\n🎉 Thank you, {form_data.get('name', 'Customer')}!\n\n📋 **Your Details:**\n• Name: {form_data.get('name', 'N/A')}\n• Mobile No: {form_data.get('mobile_number', 'N/A')}\n• Alternate No: {form_data.get('alternate_number', 'N/A')}\n• Vehicle No: {form_data.get('vehicle_number', 'N/A')}\n• Service: {form_data.get('service_type', 'N/A')}\n• Location: {form_data.get('location', 'N/A')}\n\n🔧 **What's Next:**\n• Our team will contact you within 24 hours\n• We'll confirm your appointment details\n• Service will be scheduled at your convenience\n\n📞 **Need Help?**\nCall us: +91-XXXXXXXXXX\n\n🙏 Thank you for choosing our service!"

                response_data["text"] = thank_you_message
                response_data["llm"] = thank_you_message
                response_data["function_response_id"] = 1
                response_data["functin_response"] = []

                logger.info(f"✅ Form submission processed successfully for {form_data.get('name', 'Customer')}")
            else:
                error_message = "❌ **Submission Failed**\n\nSorry, there was an issue processing your request. Please try again or contact us directly.\n\n📞 Call us: +91-XXXXXXXXXX"
                response_data["text"] = error_message
                response_data["llm"] = error_message
                response_data["function_response_id"] = 1
                response_data["functin_response"] = []

                logger.error(f"❌ Form submission failed for {form_data.get('name', 'Customer')}")

                # Return early - don't process as regular query
                return JSONResponse(content={
                    'generated_text': response_data["text"],
                    'function_response_id': response_data["function_response_id"],
                    'function_response': response_data["functin_response"]
                })

    except (json.JSONDecodeError, TypeError):
                # Not a JSON form submission, continue with regular processing
                pass

            # Check for live agent requests FIRST (highest priority)
    if prompt.lower() in ["live agent", "liveagent", "live agent handover", "handover", "talk with live agent", "connect to agent", "human agent", "speak to agent"]:
                logger.info(f"🤝 Live agent request detected: '{prompt}'")

                # Parse user ID for handover
                actual_user_id, session_id_parsed, project_id = parse_user_id(user_id)

                # Update handover status
                handover_result = update_handover("bhandari", actual_user_id, True)

                if "error" not in handover_result:
                    logger.info(f"✅ Live agent handover completed for user {user_id}")
                    response_data["text"] = "🤝 **Agent Handover Completed**\n\nYou are now connected to a live agent who will assist you with your automotive needs.\n\nPlease wait a moment for an agent to respond."
                    response_data["llm"] = response_data["text"]
                    response_data["function_response_id"] = 1
                    response_data["functin_response"] = []
                else:
                    logger.error(f"❌ Live agent handover failed for user {user_id}: {handover_result}")
                    response_data["text"] = "❌ **Unable to Connect to Live Agent**\n\nWe're experiencing technical difficulties. Please try again later or contact us directly."
                    response_data["llm"] = response_data["text"]
                    response_data["function_response_id"] = 1
                    response_data["functin_response"] = []
    elif "|" in prompt:
                logger.info(f"🔧 Detected car-specific action format: '{prompt}'")
                parts = prompt.split("|", 1)
                if len(parts) == 2:
                    car_name = parts[0].strip()
                    action = parts[1].strip()

                    logger.info(f"🚗 Car-specific action: {car_name} -> {action}")

                    # Try to handle car-specific action using new car data system (NO NAMESPACING)
                    car_response = get_car_response_with_media_id(car_name, action, user_id)

                    if car_response:
                        response_data["text"] = car_response["message"]
                        response_data["llm"] = car_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = car_response.get("buttons", [])

                        # Add media_id if present
                        if "media_id" in car_response:
                            response_data["media_id"] = car_response["media_id"]

                        logger.info(f"✅ Found car action using new system: {car_name} -> {action}")
                    else:
                        # Fall through to regular flow if car-specific action fails
                        whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                        if whatsapp_response:
                            response_data["text"] = whatsapp_response["message"]
                            response_data["llm"] = whatsapp_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["functin_response"] = whatsapp_response.get("buttons", [])
                            # Add media_id if present in whatsapp response
                            if "media_id" in whatsapp_response:
                                response_data["media_id"] = whatsapp_response["media_id"]
                else:
                    # Invalid format, fall through to regular flow
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        response_data["text"] = whatsapp_response["message"]
                        response_data["llm"] = whatsapp_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = whatsapp_response.get("buttons", [])
                        # Add media_id if present in whatsapp response
                        if "media_id" in whatsapp_response:
                            response_data["media_id"] = whatsapp_response["media_id"]
                    else:
                         logger.info(f"🔍 Processing regular prompt (no | format): '{prompt}'")

                # Check for service-related queries THIRD (high priority)
                prompt_lower = prompt.lower()
                if any(pattern in prompt_lower for pattern in ['i want to book a service', 'i need a service', 'i want service', 'need service']):
                    logger.info(f"🔧 Service query detected: '{prompt}' - routing to Book a Service flow")
                    base_response = get_whatsapp_response("Book a Service", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Service flow not found, falling back to LLM")

                # Check for test drive queries
                elif any(pattern in prompt_lower for pattern in ['i want to book a test drive', 'i want test drive', 'i want to test drive']):
                    logger.info(f"🚗 Test drive query detected: '{prompt}' - routing to Book a Test Drive flow")
                    base_response = get_whatsapp_response("Book a Test Drive", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Test drive flow not found, falling back to LLM")

                # Check for call back queries
                elif any(pattern in prompt_lower for pattern in ['i want a call back', 'i need a call back', 'i want call back']):
                    logger.info(f"📞 Call back query detected: '{prompt}' - routing to Request a Call Back flow")
                    base_response = get_whatsapp_response("Request a Call Back", user_id)
                    if base_response:
                        response_data["text"] = base_response["message"]
                        response_data["llm"] = base_response["message"]
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = base_response.get("buttons", [])
                        if "media_id" in base_response:
                            response_data["media_id"] = base_response["media_id"]
                    else:
                        logger.warning("❌ Call back flow not found, falling back to LLM")

                # Check for image queries (car model + images/interior/exterior)
                elif any(img_word in prompt_lower for img_word in ['image', 'images', 'interior', 'exterior', 'photo', 'photos', 'picture', 'pictures']):
                    # Extract car name from the query using fuzzy matching
                    car_name = None

                    # Use fuzzy matching to find car names in the query
                    fuzzy_matched_cars = find_car_matches(prompt)
                    if fuzzy_matched_cars:
                        car_name = fuzzy_matched_cars[0]  # Take the first match
                        logger.info(f"📸 Car name extracted from query using fuzzy matching: '{prompt}' -> {car_name}")

                    # If no car name found in prompt, use user's car context
                    if not car_name and user_id:
                        current_car = get_user_context(user_id)
                        if current_car:
                            car_name = current_car
                            logger.info(f"📸 Using car context for image query: '{prompt}' -> {car_name}")

                    if car_name:
                        # IMPORTANT: Update user context immediately when they ask for a specific car's images
                        if user_id and fuzzy_matched_cars:  # Only update if car was mentioned in query
                            set_user_context(user_id, car_name)
                            update_user_car_context(user_id, car_name)
                            logger.info(f"🎯 Updated user context for image query: {user_id} -> {car_name}")

                        logger.info(f"📸 Image query detected for {car_name}: '{prompt}' - routing to car WhatsApp flow")

                        # Determine the specific action based on query
                        if 'interior' in prompt_lower:
                            action = 'Interior'
                        elif 'exterior' in prompt_lower:
                            action = 'Exterior'
                        else:
                            action = 'Gallery'  # Default to gallery for general image requests

                        # Try to get car response with specific action
                        car_response = get_car_response_with_media_id(car_name, action, user_id)
                        if car_response:
                            response_data["text"] = car_response["message"]
                            response_data["llm"] = car_response["message"]
                            response_data["function_response_id"] = 1
                            response_data["functin_response"] = car_response.get("buttons", [])
                            if "media_id" in car_response:
                                response_data["media_id"] = car_response["media_id"]
                            logger.info(f"✅ Found {action} images for {car_name}")
                        else:
                            # Fallback to general car flow
                            car_response = get_car_response_with_media_id(car_name, None, user_id)
                            if car_response:
                                response_data["text"] = car_response["message"]
                                response_data["llm"] = car_response["message"]
                                response_data["function_response_id"] = 1
                                response_data["functin_response"] = car_response.get("buttons", [])
                                if "media_id" in car_response:
                                    response_data["media_id"] = car_response["media_id"]
                                logger.info(f"✅ Found general car flow for {car_name}")
                            else:
                                logger.warning(f"❌ No car flow found for {car_name}, falling back to LLM")
                    else:
                        logger.info(f"📸 Image query detected but no car name found and no user context: '{prompt}'")
                        # Continue to other handlers

                        # Still add comparison buttons even if detailed comparison fails
                        comparison_buttons = generate_comparison_buttons(prompt)
                        if comparison_buttons:
                            response_data["functin_response"] = comparison_buttons
                            response_data["function_response_id"] = 1
                else:
                    # PRIORITY: Check for conversational form triggers first
                    prompt_lower = prompt.lower().strip()

                    # Check if the prompt matches any WhatsApp flow step directly
                    logger.info(f"🔍 Checking WhatsApp flow match for: '{prompt}'")
                    whatsapp_response = check_whatsapp_flow_match(prompt, user_id)
                    if whatsapp_response:
                        logger.info(f"✅ Found WhatsApp flow match for: '{prompt}'")

                        # Enhance WhatsApp responses with explanatory text if missing
                        whatsapp_message = whatsapp_response.get("message", "")

                        # Add explanatory text for common button flows
                        if not whatsapp_message.strip():
                            if prompt.lower() == "main menu":
                                whatsapp_message = "🏠 **Welcome to Bhandari Automobiles!**\n\nWe are your trusted Toyota and Tata Commercial vehicle dealer in Kolkata. Choose from the options below:"
                            elif "Toyota" in prompt.lower():
                                whatsapp_message = "🏟️ **Toyota Cars - Affordable & Reliable**\n\nExplore our Toyota range of budget-friendly cars perfect for everyday use:"
                            elif "Toyota" in prompt.lower():
                                whatsapp_message = "✨ **Toyota Cars - Premium & Sophisticated**\n\nDiscover our premium Toyota collection with advanced features:"
                            elif "tata" in prompt.lower() and "commercial" in prompt.lower():
                                whatsapp_message = "🚛 **Tata Commercial Vehicles**\n\nPowerful commercial vehicles for your business needs:"

                        response_data["text"] = whatsapp_message
                        response_data["llm"] = whatsapp_message
                        response_data["function_response_id"] = 1
                        response_data["functin_response"] = whatsapp_response.get("buttons", [])
                    else:
                        logger.info(f"❌ No WhatsApp flow match found for: '{prompt}'")

                        # PRIORITY 1: Check for budget queries first (before universal vehicle handler)
                        prompt_lower = prompt.lower()

                        # Check for explicit budget keywords
                        explicit_budget = any(phrase in prompt_lower for phrase in ['under', 'from', 'budget', 'starting', 'below', 'above', 'between', 'within', 'maximum', 'minimum']) and any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l'])

                        # Check for implicit budget patterns like "i have 10l i want suv"
                        implicit_budget = any(phrase in prompt_lower for phrase in ['i have', 'my budget is', 'budget of']) and any(price_word in prompt_lower for price_word in ['lakh', 'lakhs', 'l']) and any(vehicle_word in prompt_lower for vehicle_word in ['car', 'suv', 'truck', 'van', 'bus', 'vehicle'])

                        if explicit_budget or implicit_budget:
                            budget_result = handle_universal_budget_query(prompt, user_id)
                            if budget_result:
                                budget_type = "Explicit" if explicit_budget else "Implicit"
                                logger.info(f"✅ {budget_type} budget query match: '{prompt}'")
                                response_data["text"] = budget_result["message"]
                                response_data["llm"] = budget_result["message"]
                                response_data["function_response_id"] = 1
                                response_data["functin_response"] = budget_result.get("buttons", [])
                            else:
                                logger.warning(f"⚠️ Budget query detected but handler returned None: '{prompt}'")
                                # Fall through to universal vehicle handler
                                universal_vehicle_response = handle_universal_vehicle_query(prompt, user_id)
                                if universal_vehicle_response:
                                    logger.info(f"✅ Found universal vehicle match for: '{prompt}'")
                                    response_data["text"] = universal_vehicle_response["message"]
                                    response_data["llm"] = universal_vehicle_response["message"]
                                    response_data["function_response_id"] = 1
                                    response_data["functin_response"] = universal_vehicle_response.get("buttons", [])
                        else:
                            # PRIORITY 2: Check for specific car mentions before universal handler
                            car_mentioned = None
                            prompt_lower = prompt.lower()
                            
                            # Check if any specific car is mentioned
                            for cname in CAR_DATA_REGISTRY.keys():
                                if cname != 'Main Menu' and cname.lower() in prompt_lower:
                                    car_mentioned = cname
                                    break
                            
                            # Also check name mappings
                            if not car_mentioned:
                                for mapped_name, canonical_name in CAR_NAME_MAPPINGS.items():
                                    if mapped_name in prompt_lower:
                                        car_mentioned = canonical_name
                                        break
                            
                            if car_mentioned:
                                logger.info(f"🚗 Specific car mentioned: '{car_mentioned}' in prompt: '{prompt}'")
                                car_response = get_car_response_with_media_id(car_mentioned, user_id=user_id)
                                if car_response:
                                    response_data["text"] = car_response["message"]
                                    response_data["llm"] = car_response["message"]
                                    response_data["function_response_id"] = 1
                                    response_data["functin_response"] = car_response.get("buttons", [])
                                    if "media_id" in car_response:
                                        response_data["media_id"] = car_response["media_id"]
                                    # Set user context
                                    if user_id:
                                        set_user_context(user_id, car_mentioned)
                                        update_user_car_context(user_id, car_mentioned)
                                else:
                                    # Use Universal Vehicle Query Handler as fallback
                                    universal_vehicle_response = handle_universal_vehicle_query(prompt, user_id)
                                    if universal_vehicle_response:
                                        logger.info(f"✅ Found universal vehicle match for: '{prompt}'")
                                        response_data["text"] = universal_vehicle_response["message"]
                                        response_data["llm"] = universal_vehicle_response["message"]
                                        response_data["function_response_id"] = 1
                                        response_data["functin_response"] = universal_vehicle_response.get("buttons", [])
                            else:
                                # PRIORITY 3: Use Universal Vehicle Query Handler - works for ALL vehicle types
                                universal_vehicle_response = handle_universal_vehicle_query(prompt, user_id)
                                if universal_vehicle_response:
                                    logger.info(f"✅ Found universal vehicle match for: '{prompt}'")
                                    response_data["text"] = universal_vehicle_response["message"]
                                    response_data["llm"] = universal_vehicle_response["message"]
                                    response_data["function_response_id"] = 1
                                    response_data["functin_response"] = universal_vehicle_response.get("buttons", [])

                                # No specific handlers matched - call AI model for conversational queries
                                logger.info(f"🤖 No specific handlers matched, calling AI for: '{prompt}'")

                                if model is None:
                                    # Try to handle the query with available functions even without AI
                                    logger.info(f"🔧 AI model not available, trying direct function handling for: '{prompt}'")

                                    # First: try to detect a car name in the (possibly misspelled) prompt using fuzzy matching
                                    try:
                                        prompt_lower = prompt.lower()
                                        fuzzy_matches = find_car_matches(prompt)
                                    except Exception:
                                        fuzzy_matches = []

                                    if fuzzy_matches:
                                        # Return the first fuzzy-matched car's WhatsApp flow
                                        detected_car = fuzzy_matches[0]
                                        logger.info(f"🔍 Model offline — fuzzy-detected car: '{detected_car}' for prompt '{prompt}'")
                                        car_response = get_car_response_with_media_id(detected_car, user_id=user_id)
                                        if car_response:
                                            response_data["text"] = car_response.get("message", "")
                                            response_data["llm"] = response_data["text"]
                                            response_data["function_response_id"] = 1
                                            response_data["functin_response"] = car_response.get("buttons", [])
                                            if "media_id" in car_response:
                                                response_data["media_id"] = car_response["media_id"]
                                            # Update user context
                                            if user_id:
                                                set_user_context(user_id, detected_car)
                                                update_user_car_context(user_id, detected_car)
                                            logger.info(f"✅ Returned local car flow for '{detected_car}' while model offline")
                                        else:
                                            # If local car flow failed, fall back to available-cars handler below
                                            fuzzy_matches = []

                                    # If no fuzzy car match, handle general 'what cars' queries
                                    if not fuzzy_matches and any(phrase in prompt_lower for phrase in ['what cars', 'available cars', 'show cars', 'cars available', 'all cars']):
                                        logger.info("🚗 Direct get_all_cars() call without AI")
                                        result = get_all_cars()

                                        if result["status"] == "success" and result["cars"]:
                                            formatted_response = format_all_cars_message(result)
                                            function_response = formatted_response["message"]

                                            # Handle WhatsApp 10-item limit
                                            total_cars = len(result["cars"])
                                            if total_cars > 10:
                                                whatsapp_buttons = [{
                                                    "data": ["Toyota Cars", "Toyota Cars"],
                                                    "data_type": "list",
                                                    "message": "Choose a dealership category:"
                                                }]
                                            else:
                                                car_names = [car.get('name', 'Unknown Car') for car in result["cars"]]
                                                whatsapp_buttons = [{
                                                    "data": car_names,
                                                    "data_type": "list",
                                                    "message": "📋 Select a car for details:"
                                                }]

                                            response_data["text"] = function_response
                                            response_data["llm"] = function_response
                                            response_data["function_response_id"] = 1
                                            response_data["functin_response"] = whatsapp_buttons
                                            logger.info("✅ Successfully handled cars query without AI")
                                        else:
                                            response_data["text"] = "❌ No cars available at the moment"
                                            response_data["llm"] = response_data["text"]
                                    elif not fuzzy_matches:
                                        # No local handler matched — preserve previous behaviour but with clearer log
                                        logger.warning("❌ AI model is unavailable and no local handler matched the prompt")
                                        response_data["text"] = "❌ *Service Temporarily Unavailable*\n\nThe AI assistant is currently not available. Please try again later."
                                        response_data["llm"] = response_data["text"]
                                else:
                                    chat_session = model.start_chat(history=SESSION_ID[user_id])
                                    response = chat_session.send_message(prompt)

                                    if response.candidates and response.candidates[0].content.parts:
                                        part = response.candidates[0].content.parts[0]

                                        # Store the original AI text response first
                                        ai_text_response = part.text if part.text else ""

                                        # Set default response
                                        response_data["text"] = ai_text_response
                                        response_data["llm"] = ai_text_response

                                        # Process function calls from AI
                                        if hasattr(part, "function_call") and part.function_call:
                                            fn = part.function_call.name
                                            args = part.function_call.args
                                            logger.info(f"🔧 AI function call: {fn} with args: {args}")

                                            if fn == "get_all_cars":
                                                # Get all cars from both Toyota and Toyota
                                                logger.info("🚗 Calling get_all_cars() function")
                                                result = get_all_cars()
                                                logger.info(f"🚗 get_all_cars() result: status={result.get('status')}, cars_count={len(result.get('cars', []))}")

                                                if result["status"] == "success" and result["cars"]:
                                                    logger.info("✅ get_all_cars() success - formatting response")
                                                    # Format the response with interactive buttons using the dedicated function
                                                    formatted_response = format_all_cars_message(result)
                                                    function_response = formatted_response["message"]

                                                    # WhatsApp has a 10-item limit, so show categories instead of all cars
                                                    total_cars = len(result["cars"])
                                                    if total_cars > 10:
                                                        logger.info(f"🔧 {total_cars} cars exceed WhatsApp limit (10), showing categories instead")
                                                        # Create category selection buttons (like Main Menu)
                                                        whatsapp_buttons = [{
                                                            "data": ["Toyota Cars", "Toyota Cars"],
                                                            "data_type": "list",
                                                            "message": "Choose a dealership category:"
                                                        }]
                                                    else:
                                                        # Show all cars if within limit
                                                        car_names = [car.get('name', 'Unknown Car') for car in result["cars"]]
                                                        logger.info(f"🔧 Creating WhatsApp buttons for {len(car_names)} cars: {car_names[:3]}...")
                                                        whatsapp_buttons = [{
                                                            "data": car_names,
                                                            "data_type": "list",
                                                            "message": "📋 Select a car for details:"

                                                        }]
                                                    # Override the buttons from utility.py
                                                    formatted_response["buttons"] = whatsapp_buttons

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    logger.info(f"🔧 Setting response_data text to: {combined_response[:100]}...")
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for all cars to ensure WhatsApp flow works
                                                    if formatted_response.get("buttons"):
                                                        logger.info(f"🔧 Setting response_data buttons: {len(formatted_response['buttons'])} buttons")
                                                        response_data["function_response_id"] = 1
                                                        response_data["functin_response"] = formatted_response["buttons"]
                                                    logger.info(f"🔧 response_data after get_all_cars: {response_data}")
                                                else:
                                                    logger.warning(f"❌ get_all_cars() failed: status={result.get('status')}, cars={len(result.get('cars', []))}")
                                                    function_response = "❌ No cars available at the moment"
                                                    response_data["text"] = function_response
                                                    response_data["llm"] = function_response

                                            elif fn == "search_cars":
                                                query = args.get("query", "")
                                                logger.info(f"🔍 AI calling search_cars with query: '{query}'")

                                                # Use AI-powered search
                                                result = search_cars(query)

                                                if result["status"] == "success" and result["cars"]:
                                                    formatted_response = utility.format_search_results_message(result)
                                                    function_response = formatted_response["message"]

                                                    # Handle WhatsApp 10-item limit for search results
                                                    if formatted_response.get("buttons"):
                                                        buttons = formatted_response["buttons"]
                                                        if buttons and len(buttons) > 0:
                                                            car_list = buttons[0].get("data", [])
                                                            if len(car_list) > 10:
                                                                logger.info(f"🔧 Search results ({len(car_list)} items) exceed WhatsApp limit, showing top 10")
                                                                # Show top 10 results
                                                                top_cars = car_list[:10]
                                                                buttons = [{
                                                                    "data": top_cars,
                                                                    "data_type": "list",
                                                                    "message": f"Top {len(top_cars)} matches (showing first 10):"
                                                                }]
                                                                formatted_response["buttons"] = buttons

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for search results to ensure WhatsApp flow works
                                                    if formatted_response.get("buttons"):
                                                        response_data["function_response_id"] = 1
                                                        response_data["functin_response"] = formatted_response["buttons"]
                                                else:
                                                    # Let LLM handle no results naturally with search context
                                                    function_response = f"No cars found matching '{query}'. Search criteria: {result.get('criteria', {})}"
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response

                                            elif fn == "show_car_details":
                                                car_id = args.get("car_id", "")
                                                logger.info(f"🚗 AI calling show_car_details for: '{car_id}'")

                                                # First check if this car exists in WhatsApp flow
                                                whatsapp_car_response = check_whatsapp_flow_match(car_id, user_id)
                                                if whatsapp_car_response:
                                                    function_response = whatsapp_car_response.get("message", "")

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    response_data["function_response_id"] = 1
                                                    response_data["functin_response"] = whatsapp_car_response.get("buttons", [])
                                                    function_calls_processed = True
                                                else:
                                                    # Fallback to car data manager
                                                    result = show_car_details_with_buttons(car_id, user_id)
                                                    function_response = result.get("message", "")

                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response

                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    # Always show buttons for car details to ensure WhatsApp flow works
                                                    if result.get("buttons"):
                                                        response_data["function_response_id"] = 1
                                                        response_data["functin_response"] = result["buttons"]
                                            
                                            elif fn == "compare_cars":
                                                car1 = args.get("car1", "")
                                                car2 = args.get("car2", "")
                                                logger.info(f"⚖️ AI calling compare_cars: '{car1}' vs '{car2}'")
                                                
                                                from unified_car_system import compare_cars
                                                result = compare_cars(car1, car2)
                                                
                                                if result["status"] == "success":
                                                    function_response = result["comparison"]
                                                    
                                                    # Combine AI explanation with function response
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response
                                                    
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response
                                                    function_calls_processed = True
                                                    
                                                    # Create buttons for individual car details
                                                    comparison_buttons = [{
                                                        "data": [result["car1"], result["car2"]],
                                                        "data_type": "list",
                                                        "message": f"Select a car for detailed information:"
                                                    }]
                                                    
                                                    response_data["function_response_id"] = 1
                                                    response_data["functin_response"] = comparison_buttons
                                                else:
                                                    function_response = f"❌ Unable to compare '{car1}' and '{car2}'"
                                                    if ai_text_response.strip():
                                                        combined_response = f"{ai_text_response}\n\n{function_response}"
                                                    else:
                                                        combined_response = function_response
                                                    response_data["text"] = combined_response
                                                    response_data["llm"] = combined_response

                                            else:
                                                logger.warning(f"❌ Unknown AI function call: {fn}")
                                                # Keep the AI's text response but add a note
                                                response_data["text"] = ai_text_response + "\n\n(Note: Function call not processed)" if ai_text_response else "Function call not processed"
                                                response_data["llm"] = response_data["text"]

                                        else:
                                            # No function call, just use AI text response
                                            response_data["text"] = ai_text_response
                                            response_data["llm"] = ai_text_response

                                    else:
                                        response_data["text"] = "❌ AI could not generate a response. Please try again."
                                        response_data["llm"] = response_data["text"]

                                    # Extract token counts (similar to bitenxt_api.py)
                                    if response.usage_metadata:
                                        query_token_count = response.usage_metadata.prompt_token_count
                                        llm_token_count = response.usage_metadata.candidates_token_count
                                        total_token_count = response.usage_metadata.total_token_count
                                        logger.info(f"🔢 Token usage - Input: {query_token_count}, Output: {llm_token_count}, Total: {total_token_count}")
                                    else:
                                        query_token_count = 0
                                        llm_token_count = 0

                        # Check for live agent handover status
                        print("liveagent_status = ", liveagent_status)

                        if liveagent_status == 1:
                            # User should be handed over to live agent
                            print("Update directly to DB")

                            query_token_count = len(prompt)
                            session_id_full = f"{user_id}_{session_id}"
                            llm_response_text = ""   # No AI reply
                            llm_token_count = 0      # Since no AI was called

                            # Insert user message into DB using sync version
                            new_message_id = sync_insert_chat_message(
                                actual_user_id,
                                session_id_full,
                                project_id if project_id else None,  # handle empty project_id
                                prompt,
                                query_token_count,
                                llm_response_text,
                                llm_token_count
                            )

                            print("live_agent DB Update ", new_message_id)

                            # Flag this entry for live agent
                            update_status = await insert_liveagentFlag(new_message_id)
                            print("Live agent flag Updated status = ", update_status)

                            # Return response (empty AI text, only DB ID)
                            return JSONResponse(content={
                                "user_id": user_id,       # keep original ID
                                "generated_text": "",     # no AI text since live agent takeover
                                "db_message_id": new_message_id
                            })

        # If not handed over, continue with AI response
        # Save conversation to in-memory session history
    if user_id in SESSION_ID and response_data.get("llm"):
            SESSION_ID[user_id].append({"role": "user", "parts": [prompt]})
            SESSION_ID[user_id].append({"role": "model", "parts": [response_data["llm"]]})

        # Debug logging for response_data
    logger.info(f"🔍 Final response_data contents: {response_data}")

    final_response = {
            'generated_text': response_data.get("text", ""),
            'function_response_id': response_data.get("function_response_id", ""),
            'function_response': response_data.get("functin_response", [])
        }

    logger.info(f"🔍 Final response being returned: {final_response}")

    # Add media_id to final response if it exists in response_data
    if "media_id" in response_data:
        final_response["media_id"] = response_data["media_id"]

        # Cache the response for future use (skip greetings and user-specific responses)
        # if not is_greeting(prompt) and not any(word in prompt.lower() for word in ['my', 'i am', 'i want to book', 'call back']):
        #     cache_response(prompt, user_id, final_response)

        # WHATSAPP FIX: If we have both generated_text and function_response,
        # combine them so WhatsApp shows the full message
        if (final_response.get('generated_text') and
            final_response.get('function_response') and
            len(final_response['function_response']) > 0):

            # Get the generated text (main content)
            generated_text = final_response['generated_text']

            # Update the first function_response item's message to include generated_text
            if isinstance(final_response['function_response'][0], dict):
                original_message = final_response['function_response'][0].get('message', '')

                # Smart duplication detection: check if generated_text content is already present
                # Remove extra whitespace and newlines for comparison
                generated_clean = ' '.join(generated_text.split())
                original_clean = ' '.join(original_message.split()) if original_message else ''

                if original_message and generated_clean in original_clean:
                    # If generated_text content is already in original_message, just use original_message
                    combined_message = original_message
                elif original_message and len(generated_clean) > 0:
                    # Check if original message is significantly longer than generated text (indicating it might already contain it)
                    if len(original_clean) > len(generated_clean) * 1.5:
                        # Original message is much longer, likely already contains the content
                        combined_message = original_message
                    else:
                        # Safe to combine
                        combined_message = f"{generated_text}\n\n{original_message}"
                else:
                    # If no original message, just use generated_text
                    combined_message = generated_text

                # Update the message in function_response
                final_response['function_response'][0]['message'] = combined_message

        # Log conversation to database (similar to bitenxt_api.py pattern)
        try:
            response_time_ms = int((time.time() - request_start_time) * 1000)

            # Extract token counts (if available from LLM response)
            # These will be set by the LLM calls above if usage_metadata is available
            query_token_count = getattr(locals(), 'query_token_count', 0)
            llm_token_count = getattr(locals(), 'llm_token_count', 0)

            # Determine interaction type and car context
            interaction_type = "unknown"
            car_name = None

            if is_greeting(prompt):
                interaction_type = "greeting"
            elif final_response.get('function_response'):
                func_resp = final_response['function_response'][0] if final_response['function_response'] else {}
                data_type = func_resp.get('data_type', '')

                if data_type == 'button':
                    interaction_type = "button_click"
                elif data_type == 'image':
                    interaction_type = "gallery_view"
                elif data_type == 'video_btn':
                    interaction_type = "car_view"
                    # Try to extract car name from response
                    message = func_resp.get('message', '')
                else:
                    interaction_type = "response_view"
            else:
                interaction_type = "text_query"

            # Get current car context if not already determined
            if not car_name:
                car_name = get_user_car_context(actual_user_id)
                if car_name == 'Not specified':
                    car_name = None

            # Log database operations in background thread to avoid blocking response
            import threading

            def log_to_database():
                try:
                    # Log the complete conversation (user query + bot response) to chat_history
                    llm_response_text = final_response.get('generated_text', '')
                    message_id = sync_insert_chat_message(
                        actual_user_id,
                        session_id,
                        project_id + "-wp" if project_id else "whatsapp",
                        prompt,
                        query_token_count,
                        llm_response_text,
                        llm_token_count,
                        response_time_ms,
                        car_name,
                        interaction_type
                    )

                    # Log user interaction
                    interaction_data = {
                        "prompt": prompt,
                        "response_type": interaction_type,
                        "functin_response": final_response.get('function_response', []),
                        "media_id": final_response.get('media_id')
                    }

                    sync_insert_user_interaction(
                        actual_user_id,
                        session_id,
                        interaction_type,
                        interaction_data,
                        car_name
                    )

                    if message_id:
                        logger.info(f"✅ Chat message saved with ID: {message_id}")
                except Exception as db_error:
                    logger.warning(f"Background database logging failed: {str(db_error)}")

            # Start database logging in background thread (non-blocking)
            db_thread = threading.Thread(target=log_to_database, daemon=True)
            db_thread.start()

        except Exception as db_error:
            logger.warning(f"Database logging failed: {db_error}")

        return JSONResponse(content=final_response)


if __name__ == '__main__':
    uvicorn.run("old_code:app", host='0.0.0.0', port=8039, log_level="info")