"""
Unified Car Query System
Combines data from JSON files and cars_data.py to provide seamless car information
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Union
from difflib import SequenceMatcher

# Import car data from cars_data.py
try:
    from cars.cars_data import cars_data
except ImportError:
    cars_data = {}

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedCarSystem:
    """Unified system for querying car data from both JSON files and cars_data.py"""
    
    def __init__(self, json_data_dir: str = "cars"):
        self.json_data_dir = json_data_dir
        self.json_cars = {}
        self.python_cars = cars_data
        self.car_mappings = {}
        self._load_json_data()
        self._create_car_mappings()
    
    def _load_json_data(self):
        """Load car data from JSON files"""
        try:
            if not os.path.exists(self.json_data_dir):
                logger.warning(f"JSON data directory not found: {self.json_data_dir}")
                return
                
            for filename in os.listdir(self.json_data_dir):
                if filename.endswith('.json'):
                    try:
                        filepath = os.path.join(self.json_data_dir, filename)
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            car_name = os.path.splitext(filename)[0].lower()
                            self.json_cars[car_name] = data
                    except Exception as e:
                        logger.error(f"Error loading {filename}: {e}")
                        
            logger.info(f"Loaded {len(self.json_cars)} JSON car files")
        except Exception as e:
            logger.error(f"Error loading JSON data: {e}")
    
    def _create_car_mappings(self):
        """Create mappings for car name variations"""
        # Common car name variations and aliases
        self.car_mappings = {
            # Exact matches
            'camry': 'camry',
            'fortuner': 'fortuner', 
            'glanza': 'glanza',
            'hilux': 'hilux',
            'hyryder': 'hyryder',
            'urban cruiser hyryder': 'hyryder',
            'innova crysta': 'innova_crysta',
            'innova hycross': 'innova_hycross',
            'land cruiser': 'land_cruiser',
            'land cruiser 300': 'land_cruiser',
            'legender': 'legender',
            'fortuner legender': 'legender',
            'rumion': 'rumion',
            'taisor': 'taisor',
            'urban cruiser taisor': 'taisor',
            'vellfire': 'vellfire',
            
            # Common variations
            'innova': 'innova_hycross',  # Default to newer model
            'crysta': 'innova_crysta',
            'hycross': 'innova_hycross',
            'land cruiser 300': 'land_cruiser',
            'lc300': 'land_cruiser',
            'urban cruiser': 'hyryder',  # Default to Hyryder
        }
    
    def _normalize_car_name(self, car_name: str) -> str:
        """Normalize car name for matching"""
        return car_name.lower().strip().replace('-', ' ').replace('_', ' ')
    
    def _find_best_match(self, query: str) -> Optional[str]:
        """Find the best matching car name using fuzzy matching"""
        query_normalized = self._normalize_car_name(query)
        
        # First check direct mappings
        if query_normalized in self.car_mappings:
            return self.car_mappings[query_normalized]
        
        # Check if query is contained in any car name
        all_car_names = set(self.json_cars.keys()) | set(self.python_cars.keys())
        for car_name in all_car_names:
            if query_normalized in car_name or car_name in query_normalized:
                return car_name
        
        # Fuzzy matching as fallback
        best_match = None
        best_score = 0.6  # Minimum similarity threshold
        
        for car_name in all_car_names:
            score = SequenceMatcher(None, query_normalized, car_name).ratio()
            if score > best_score:
                best_score = score
                best_match = car_name
        
        return best_match
    
    def get_car_info(self, car_name: str) -> Dict[str, Any]:
        """Get comprehensive car information from both sources"""
        matched_name = self._find_best_match(car_name)
        
        if not matched_name:
            return {
                "status": "error",
                "message": f"Sorry, I couldn't find information about '{car_name}'. Available cars: {', '.join(self.get_available_cars())}",
                "available_cars": self.get_available_cars()
            }
        
        result = {
            "status": "success",
            "car_name": matched_name,
            "query": car_name,
            "sources": {}
        }
        
        # Get data from JSON files (interactive data)
        if matched_name in self.json_cars:
            result["sources"]["json"] = self.json_cars[matched_name]
            result["interactive_data"] = self.json_cars[matched_name]
        
        # Get data from Python file (specifications)
        if matched_name in self.python_cars:
            result["sources"]["python"] = self.python_cars[matched_name]
            result["specifications"] = self.python_cars[matched_name]
        
        # Create unified response
        result["response"] = self._create_unified_response(result)
        
        return result
    
    def _create_unified_response(self, car_data: Dict[str, Any]) -> str:
        """Create a unified response combining both data sources"""
        car_name = car_data["car_name"]
        specs = car_data.get("specifications", {})
        interactive = car_data.get("interactive_data", {})
        
        # Start with basic info from specifications
        response = f"🚗 **{specs.get('name', car_name.title())}**\n\n"
        
        if specs:
            response += f"**Category:** {specs.get('category', 'N/A')}\n"
            response += f"**Fuel Types:** {', '.join(specs.get('fuel_types', ['N/A']))}\n"
            
            # Engine info
            if 'engine' in specs:
                response += "\n**Engine Options:**\n"
                for engine_type, engine_data in specs['engine'].items():
                    response += f"• {engine_type.title()}: {engine_data.get('displacement', 'N/A')} | {engine_data.get('max_power', 'N/A')} | {engine_data.get('max_torque', 'N/A')}\n"
            
            # Mileage info
            mileage_key = f"{car_name} mileage"
            if mileage_key in specs:
                response += "\n**Mileage:**\n"
                for variant, mileage in specs[mileage_key].items():
                    response += f"• {variant.replace('_', ' ').title()}: {mileage}\n"
            
            # Price info from variants
            variants_key = f"{car_name} variants"
            if variants_key in specs and specs[variants_key]:
                response += "\n**Price Range:**\n"
                variants = specs[variants_key]
                if variants:
                    min_price = min([v.get('price', '₹0') for v in variants], key=lambda x: x.replace('₹', '').replace(' Lakh', '').replace(' Crore', '').split('–')[0])
                    max_price = max([v.get('price', '₹0') for v in variants], key=lambda x: x.replace('₹', '').replace(' Lakh', '').replace(' Crore', '').split('–')[-1])
                    if min_price == max_price:
                        response += f"• Starting from {min_price}\n"
                    else:
                        response += f"• {min_price} - {max_price}\n"
        
        # Add interactive options if available
        if interactive and car_name.title() in interactive:
            main_data = interactive[car_name.title()][0] if interactive[car_name.title()] else {}
            if main_data.get('message'):
                response += f"\n**Description:** {main_data['message']}\n"
            
            if main_data.get('data'):
                response += "\n**Available Actions:**\n"
                for action in main_data['data'][:3]:  # Show first 3 actions
                    response += f"• {action}\n"
        
        response += f"\n💡 *Ask me about specific details like variants, features, colors, or book a test drive for {specs.get('name', car_name.title())}!*"
        
        return response
    
    def get_available_cars(self) -> List[str]:
        """Get list of all available cars"""
        all_cars = set()
        
        # Add from JSON files
        for car_name in self.json_cars.keys():
            all_cars.add(car_name.replace('_', ' ').title())
        
        # Add from Python data
        for car_name, car_info in self.python_cars.items():
            all_cars.add(car_info.get('name', car_name.replace('_', ' ').title()))
        
        return sorted(list(all_cars))
    
    def search_cars(self, query: str) -> List[Dict[str, Any]]:
        """Search for cars based on query"""
        query_lower = query.lower()
        results = []
        
        # Search in specifications
        for car_name, car_data in self.python_cars.items():
            if (query_lower in car_data.get('name', '').lower() or
                query_lower in car_data.get('category', '').lower() or
                any(query_lower in fuel.lower() for fuel in car_data.get('fuel_types', []))):
                
                results.append({
                    'name': car_data.get('name', car_name.title()),
                    'category': car_data.get('category', 'N/A'),
                    'fuel_types': car_data.get('fuel_types', []),
                    'source': 'specifications'
                })
        
        return results
    
    def compare_cars(self, car1: str, car2: str) -> Dict[str, Any]:
        """Compare two Toyota cars"""
        car1_info = self.get_car_info(car1)
        car2_info = self.get_car_info(car2)
        
        if car1_info["status"] != "success" or car2_info["status"] != "success":
            return {
                "status": "error",
                "message": f"Could not find information for comparison. Available cars: {', '.join(self.get_available_cars())}"
            }
        
        car1_specs = car1_info.get("specifications", {})
        car2_specs = car2_info.get("specifications", {})
        
        return {
            "status": "success",
            "car1": car1_specs.get('name', car1.title()),
            "car2": car2_specs.get('name', car2.title()),
            "comparison": self._create_comparison_text(car1_specs, car2_specs)
        }
    
    def _create_comparison_text(self, car1_specs: Dict, car2_specs: Dict) -> str:
        """Create detailed comparison text between two cars"""
        car1_name = car1_specs.get('name', 'Car 1')
        car2_name = car2_specs.get('name', 'Car 2')
        
        comparison = f"🆚 **{car1_name} vs {car2_name} Comparison**\n\n"

        # Category and Body Type
        comparison += "**Category & Body Type:**\n"
        comparison += f"• {car1_name}: {car1_specs.get('category', 'N/A')}\n"
        comparison += f"• {car2_name}: {car2_specs.get('category', 'N/A')}\n\n"

        # Fuel Types
        comparison += "**Fuel Types:**\n"
        comparison += f"• {car1_name}: {', '.join(car1_specs.get('fuel_types', ['N/A']))}\n"
        comparison += f"• {car2_name}: {', '.join(car2_specs.get('fuel_types', ['N/A']))}\n\n"

        # Engine & Performance
        comparison += "**Engine & Performance:**\n"
        # Get engine data
        car1_engine = car1_specs.get('engine', {})
        car2_engine = car2_specs.get('engine', {})
        
        if car1_engine or car2_engine:
            for engine_type in set(car1_engine.keys()) | set(car2_engine.keys()):
                comparison += f"{engine_type.title()}:\n"
                car1_eng = car1_engine.get(engine_type, {})
                car2_eng = car2_engine.get(engine_type, {})
                
                if car1_eng or car2_eng:
                    comparison += f"• {car1_name}: {car1_eng.get('displacement', 'N/A')} | {car1_eng.get('max_power', 'N/A')} | {car1_eng.get('max_torque', 'N/A')}\n"
                    comparison += f"• {car2_name}: {car2_eng.get('displacement', 'N/A')} | {car2_eng.get('max_power', 'N/A')} | {car2_eng.get('max_torque', 'N/A')}\n"
            comparison += "\n"

        # Price Range
        comparison += "**Price Range:**\n"
        def get_price_range(car_specs, car_name):
            variants_key = f"{car_name.lower()} variants"
            variants = car_specs.get(variants_key, [])
            if variants:
                prices = [v.get('price', '').replace('₹', '').replace(' Lakh', '').replace(' Crore', '').split('–') for v in variants]
                all_prices = [float(p.strip()) for sublist in prices for p in sublist if p.strip().replace('.', '').isdigit()]
                if all_prices:
                    return f"₹{min(all_prices)} - {max(all_prices)} Lakh"
            return 'N/A'

        comparison += f"• {car1_name}: {get_price_range(car1_specs, car1_name)}\n"
        comparison += f"• {car2_name}: {get_price_range(car2_specs, car2_name)}\n\n"

        # Mileage
        comparison += "**Mileage:**\n"
        car1_mileage = car1_specs.get(f"{car1_name.lower()} mileage", {})
        car2_mileage = car2_specs.get(f"{car2_name.lower()} mileage", {})
        
        if car1_mileage or car2_mileage:
            for variant in set(car1_mileage.keys()) | set(car2_mileage.keys()):
                variant_display = variant.replace('_', ' ').title()
                comparison += f"{variant_display}:\n"
                comparison += f"• {car1_name}: {car1_mileage.get(variant, 'N/A')}\n"
                comparison += f"• {car2_name}: {car2_mileage.get(variant, 'N/A')}\n"
            comparison += "\n"

        # Dimensions (if available)
        car1_dimensions = car1_specs.get('dimensions', {})
        car2_dimensions = car2_specs.get('dimensions', {})
        
        if car1_dimensions or car2_dimensions:
            comparison += "**Dimensions:**\n"
            dims = ['length', 'width', 'height', 'wheelbase', 'ground_clearance']
            for dim in dims:
                if dim in car1_dimensions or dim in car2_dimensions:
                    dim_display = dim.replace('_', ' ').title()
                    comparison += f"{dim_display}:\n"
                    comparison += f"• {car1_name}: {car1_dimensions.get(dim, 'N/A')}\n"
                    comparison += f"• {car2_name}: {car2_dimensions.get(dim, 'N/A')}\n"
            comparison += "\n"

        # Features
        car1_features = car1_specs.get('features', [])
        car2_features = car2_specs.get('features', [])
        
        if car1_features or car2_features:
            comparison += "**Key Features Comparison:**\n"
            all_features = set(car1_features) | set(car2_features)
            for feature in sorted(all_features):
                comparison += f"{feature}:\n"
                comparison += f"• {car1_name}: {'✓' if feature in car1_features else '✗'}\n"
                comparison += f"• {car2_name}: {'✓' if feature in car2_features else '✗'}\n"
            comparison += "\n"

        # Add a suggestion section
        comparison += "**Quick Summary:**\n"
        comparison += f"• {car1_name}: Best suited for {car1_specs.get('best_suited_for', 'various needs')}.\n"
        comparison += f"• {car2_name}: Best suited for {car2_specs.get('best_suited_for', 'various needs')}.\n\n"

        comparison += "💡 *For more details about either car, just ask! You can also book a test drive to experience them yourself.*"
        
        return comparison
    
    def handle_car_query(self, user_query: str) -> Dict[str, Any]:
        """Main method to handle any car-related query"""
        # Extract potential car name from query
        query_lower = user_query.lower()
        
        # Check if it's a general query
        if any(word in query_lower for word in ['all cars', 'available cars', 'list cars', 'show cars']):
            return {
                "status": "success",
                "message": f"Available Toyota cars: {', '.join(self.get_available_cars())}",
                "cars": self.get_available_cars()
            }
        
        # Try to find car name in query
        best_match = None
        for car_name in self.get_available_cars():
            if car_name.lower() in query_lower:
                best_match = car_name
                break
        
        if not best_match:
            # Try fuzzy matching with the entire query
            best_match = self._find_best_match(user_query)
        
        if best_match:
            return self.get_car_info(best_match)
        else:
            return {
                "status": "error", 
                "message": f"I couldn't identify a specific car in your query. Available cars: {', '.join(self.get_available_cars())}",
                "available_cars": self.get_available_cars()
            }

# Create singleton instance
unified_car_system = UnifiedCarSystem()

# Convenience functions
def get_car_info(car_name: str) -> Dict[str, Any]:
    """Get car information by name"""
    return unified_car_system.get_car_info(car_name)

def handle_car_query(user_query: str) -> Dict[str, Any]:
    """Handle any car-related query"""
    return unified_car_system.handle_car_query(user_query)

def get_available_cars() -> List[str]:
    """Get list of available cars"""
    return unified_car_system.get_available_cars()

def search_cars(query: str) -> List[Dict[str, Any]]:
    """Search for cars"""
    return unified_car_system.search_cars(query)

def compare_cars(car1: str, car2: str) -> Dict[str, Any]:
    """Compare two Toyota cars"""
    return unified_car_system.compare_cars(car1, car2)