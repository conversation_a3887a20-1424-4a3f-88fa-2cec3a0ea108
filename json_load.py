import json
from utility import load_text_file
from car_data_manager import get_car_data



CAMRY_DATA = get_car_data('Camry')
FORTUNER_DATA = get_car_data('Fortuner')
INNOVA_CRYSTA_DATA = get_car_data('Innova_Crysta')
INNOVA_HYCRROSS_DATA = get_car_data('Innova_Hycross')
GLANZA_DATA = get_car_data('Glanza')
LEGENDER_DATA = get_car_data('Legender')
LAND_CRUISER_DATA = get_car_data('Land Cruiser')
LEGENDER_DATA = get_car_data('Legender')
HYRYDER_DATA = get_car_data('Hyryder')
HILUX_DATA = get_car_data('Hilux')
RUMION_DATA = get_car_data('Rumion')
TAISOR_DATA = get_car_data('Taisor')
VELLFIRE_DATA = get_car_data('Vellfire')

def get_car_data_by_name(car_name):
    """Get car data by name with <PERSON><PERSON> as primary source, fallback to registry and name mappings"""
    # First try to get data from car_data_manager
    manager_data = get_car_data(car_name)
    if manager_data:
        return manager_data

    # Fallback to registry lookup
    if car_name in CAR_DATA_REGISTRY:
        return CAR_DATA_REGISTRY[car_name]

    # Try name mapping
    normalized_name = car_name.lower().strip()
    if normalized_name in CAR_NAME_MAPPINGS:
        mapped_name = CAR_NAME_MAPPINGS[normalized_name]
        # Try manager first for mapped name
        manager_data = get_car_data(mapped_name)
        if manager_data:
            return manager_data
        # Fallback to registry
        return CAR_DATA_REGISTRY.get(mapped_name, {})

    return {}


def load_car_json(file_path):
    """Load a car JSON file and return its content (fallback method)"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: Car file not found: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file {file_path}: {e}")
        return {}
    
MAIN_MENU_DATA = load_car_json('main_menu.json')
if MAIN_MENU_DATA:
        print("Main menu loaded from local JSON file")
else:
        print("Main menu not found in Redis or local JSON file")


CAR_DATA_REGISTRY = {
    # Arena Cars (currently available from Redis)
    'Camry': CAMRY_DATA,
    'Fortuner': FORTUNER_DATA,
    'Innova_Crysta': INNOVA_CRYSTA_DATA,
    'Innova_Hycross': INNOVA_HYCRROSS_DATA,
    'Glanza': GLANZA_DATA,
    'Legender': LEGENDER_DATA,
    'Land Cruiser': LAND_CRUISER_DATA,
    'Legender': LEGENDER_DATA,
    'Hyryder': HYRYDER_DATA,
    'Hilux': HILUX_DATA,
    'Rumion': RUMION_DATA,
    'Taisor': TAISOR_DATA,
    'Vellfire': VELLFIRE_DATA, 
    # Add other car types as needed
}


CAR_NAME_MAPPINGS = {
    'Camry': 'Camry',
    'fortuner': 'Fortuner',
    'innova crysta': 'Innova_Crysta',
    'innova hycross': 'Innova_Hycross',
    'glanza': 'Glanza',
    'legender': 'Legender',
    'land cruiser': 'Land Cruiser',
    'hyryder': 'Hyryder',
    'hilux': 'Hilux',
    'rumion': 'Rumion',
    'taisor': 'Taisor',
    'vellfire': 'Vellfire'
}

def load_system_prompt():
    try:
        with open('system_prompt.txt', 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        return "You are a helpful car dealership assistant for Toyota Cars. Help customers with car information, bookings, and dealership services for Toyota models."
    
def create_enhanced_system_prompt(base_prompt, knowledge_base_content):
    """Create enhanced system prompt with knowledge base content"""
    if knowledge_base_content:
        enhanced_prompt = f"""{base_prompt}

## KNOWLEDGE BASE
You have access to the following specific information about our Toyota dealership:

{knowledge_base_content}

**IMPORTANT**: When customers ask questions about details,addresses, contact information, hours, or other specific details, use the information from the Knowledge Base above to provide accurate answers. Don't just redirect to menus - answer their questions directly first, then offer additional help if appropriate.
"""
        return enhanced_prompt
    return base_prompt



def load_information_file():
    """Load information from text files for knowledge base"""
    try:
        # Load knowledge base
        knowledge_base = load_text_file("knowledge_base.txt")

        # You can also load information.txt if it exists
        # information = load_text_file("information.txt")

        # Combine both if needed
        combined_info = ""
        if knowledge_base:
            combined_info += f"Knowledge Base:\n{knowledge_base}\n\n"
        return combined_info.strip()
    except Exception as e:
        print(f"Error loading information files: {e}")
        return ""