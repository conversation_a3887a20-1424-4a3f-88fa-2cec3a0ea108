"""
Tool definitions for Gemini AI function calling
Natural language focused - let the LLM decide which function to call based on user intent
"""

TOOL_DEF = [
    {
        "function_declarations": [
            {
                "name": "get_all_cars",
                "description": "Use this when customers want to see all available cars, browse the complete inventory, or ask 'show me cars', 'what cars do you have', 'all cars', 'i want to buy a car', 'looking for cars', 'car options', etc. Shows all Toyota cars with interactive buttons.",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "search_cars",
                "description": "Use this when customers search for specific cars by name, features, fuel type, price range, or category. Examples: 'Camry', 'SUV cars', 'hybrid cars', 'cars under 20 lakhs', 'diesel cars', 'automatic cars', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The customer's search query - can be car name, fuel type, price range, category, or any car-related criteria"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "show_car_details",
                "description": "Use this when customers ask for detailed information about a specific Toyota car model. Examples: 'tell me about Cam<PERSON>', 'Fortuner details', 'show me Hyryder specs', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "car_id": {
                            "type": "string",
                            "description": "The name or ID of the specific Toyota car the customer wants details about"
                        }
                    },
                    "required": ["car_id"]
                }
            },
            {
                "name": "compare_cars",
                "description": "Use this when customers want to compare two Toyota cars. Examples: 'compare Fortuner and Camry', 'Fortuner vs Camry', 'difference between Innova and Hyryder', 'which is better Camry or Fortuner', etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "car1": {
                            "type": "string",
                            "description": "Name of the first Toyota car to compare"
                        },
                        "car2": {
                            "type": "string",
                            "description": "Name of the second Toyota car to compare"
                        }
                    },
                    "required": ["car1", "car2"]
                }
            }
        ]
    }
]