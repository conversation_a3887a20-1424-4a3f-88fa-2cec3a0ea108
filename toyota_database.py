"""
Toyota Database Module

Handles all database operations for the Toyota bot system with connection pooling,
retry logic, and secure credential management.
"""

import os
import asyncio
import logging
import json
import time
from typing import Optional, List, Dict, Tuple, Any, Union, Callable, Awaitable, TypeVar, cast
from datetime import datetime
from contextlib import asynccontextmanager
from functools import wraps

from fastapi import HTTPException, status
import requests
try:
    from pydantic import BaseSettings, PostgresDsn, validator
except ImportError:
    # Pydantic v2 compatibility
    from pydantic_settings import BaseSettings
    from pydantic import PostgresDsn, field_validator as validator

# Set up logging with better formatting
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Type variable for generic function wrapping
F = TypeVar('F', bound=Callable[..., Any])

class DatabaseSettings(BaseSettings):
    """Database configuration settings with validation."""
    
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
    DB_USER: str = os.getenv("DB_USER", "")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_NAME: str = os.getenv("DB_NAME", "toyota_db")
    DB_DSN: Optional[PostgresDsn] = None
    
    # Connection pool settings
    DB_POOL_MIN: int = int(os.getenv("DB_POOL_MIN", "2"))
    DB_POOL_MAX: int = int(os.getenv("DB_POOL_MAX", "20"))
    DB_CONN_TIMEOUT: int = int(os.getenv("DB_CONN_TIMEOUT", "10"))
    DB_COMMAND_TIMEOUT: int = int(os.getenv("DB_COMMAND_TIMEOUT", "30"))
    
    # Retry settings
    DB_RETRY_ATTEMPTS: int = int(os.getenv("DB_RETRY_ATTEMPTS", "3"))
    DB_RETRY_DELAY: float = float(os.getenv("DB_RETRY_DELAY", "1.0"))
    
    # Additional settings
    gemini_api_key: Optional[str] = os.getenv("GEMINI_API_KEY")
    meta_verify_token: Optional[str] = os.getenv("META_VERIFY_TOKEN")
    meta_app_secret: Optional[str] = os.getenv("META_APP_SECRET")
    google_sheets_id: Optional[str] = os.getenv("GOOGLE_SHEETS_ID")
    google_credentials_path: Optional[str] = os.getenv("GOOGLE_CREDENTIALS_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    # Note: pydantic v1 and v2 have different validator APIs. To keep compatibility
    # across environments, we avoid a decorated validator here and will assemble
    # the DB_DSN after creating the settings instance.

# Initialize settings
db_settings = DatabaseSettings()
# Assemble DB_DSN if not provided (compatible with pydantic v1 & v2)
if not getattr(db_settings, 'DB_DSN', None):
    try:
        db_settings.DB_DSN = (
            f"postgresql://{db_settings.DB_USER}:{db_settings.DB_PASSWORD}@{db_settings.DB_HOST}:{db_settings.DB_PORT}/{db_settings.DB_NAME}"
        )
    except Exception:
        # If any field missing, leave DB_DSN as None
        db_settings.DB_DSN = None

# Try to import asyncpg with better error handling
try:
    import asyncpg
    from asyncpg.pool import Pool
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False
    logger.warning(
        "asyncpg not available. Database functionality will be disabled. "
        "Install with: pip install asyncpg"
    )
    Pool = Any  # For type checking

# Global database pool
db_pool: Optional[Any] = None

# ============== Database Pool Management ==============

def with_retry(max_retries: int = 3, delay: float = 1.0) -> Callable[[F], F]:
    """
    Decorator to add retry logic to async functions.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds (will be doubled after each attempt)
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    # If this looks like a transient connection/timeout error, allow retry
                    is_transient = False
                    if ASYNCPG_AVAILABLE:
                        # Check common asyncpg/timeout related conditions
                        ex_type = str(type(e))
                        if ('PostgresConnectionError' in ex_type or
                                'CannotConnectNowError' in ex_type or
                                isinstance(e, asyncio.TimeoutError)):
                            is_transient = True

                    if is_transient:
                        last_exception = e
                        if attempt < max_retries - 1:
                            logger.warning(
                                f"Attempt {attempt + 1}/{max_retries} failed: {e}. Retrying in {current_delay:.1f}s..."
                            )
                            await asyncio.sleep(current_delay)
                            current_delay *= 2  # Exponential backoff
                            continue
                        # fallthrough to raising after last attempt

                    # Non-transient error or out of retries: log and raise
                    logger.error(f"Unexpected error in {func.__name__}: {e}")
                    raise

            # If we get here, all retries failed
            logger.error(f"Failed after {max_retries} attempts. Last error: {last_exception}")
            raise last_exception or Exception("Unknown error in with_retry")

        return cast(F, wrapper)

    return decorator

async def initialize_db_pool() -> None:
    """
    Initialize the database connection pool with retry logic and proper configuration.
    
    This function will attempt to establish a connection pool with the database,
    retrying on connection failures with exponential backoff.
    """
    global db_pool
    
    if not ASYNCPG_AVAILABLE:
        logger.warning("Database functionality disabled - asyncpg not available")
        return
    
    if db_pool is not None:
        logger.warning("Database pool already initialized")
        return
    
    logger.info("Initializing database connection pool...")
    
    try:
        # Create connection pool with retry logic
        db_pool = await _create_db_pool_with_retry(
            dsn=db_settings.DB_DSN,
            min_size=db_settings.DB_POOL_MIN,
            max_size=db_settings.DB_POOL_MAX,
            command_timeout=db_settings.DB_COMMAND_TIMEOUT,
            max_retries=db_settings.DB_RETRY_ATTEMPTS,
            retry_delay=db_settings.DB_RETRY_DELAY
        )
        
        # Test the connection
        async with db_pool.acquire() as conn:
            # Execute a simple query to verify the connection
            await conn.fetchval('SELECT 1')
            
        logger.info(
            f"✅ Database connection pool initialized successfully. "
            f"Pool size: {db_pool.get_min_size()}-{db_pool.get_max_size()}"
        )
        
        # Create tables if they don't exist
        await create_tables()
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize database pool: {e}")
        if db_pool:
            await db_pool.close()
            db_pool = None
        # Don't raise - allow the application to start without database

@with_retry(max_retries=3, delay=1.0)
async def _create_db_pool_with_retry(
    dsn: str,
    min_size: int = 2,
    max_size: int = 20,
    command_timeout: int = 30,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    **kwargs: Any
) -> Any:
    """
    Create a database connection pool with retry logic.
    
    Args:
        dsn: Database connection string
        min_size: Minimum number of connections in the pool
        max_size: Maximum number of connections in the pool
        command_timeout: Command timeout in seconds
        max_retries: Maximum number of retry attempts
        retry_delay: Initial delay between retries in seconds
        **kwargs: Additional arguments to pass to create_pool
        
    Returns:
        Initialized connection pool
    """
    # Remove max_retries and retry_delay from kwargs as they're not used by create_pool
    kwargs.pop('max_retries', None)
    kwargs.pop('retry_delay', None)
    
    return await asyncpg.create_pool(
        dsn=dsn,
        min_size=min_size,
        max_size=max_size,
        command_timeout=command_timeout,
        server_settings={
            'application_name': 'toyota_auto_bot',
            'tcp_keepalives_idle': '600',     # 10 minutes
            'tcp_keepalives_interval': '30',  # 30 seconds
            'tcp_keepalives_count': '3',      # 3 attempts
            'statement_timeout': str(command_timeout * 1000),  # Convert to milliseconds
            'idle_in_transaction_session_timeout': '0'  # Disable by default
        },
        **kwargs
    )

async def close_db_pool() -> None:
    """
    Close the database connection pool.
    
    This should be called during application shutdown to ensure all connections
    are properly closed.
    """
    global db_pool
    
    if db_pool:
        try:
            # Close the pool and wait for all connections to be released
            await db_pool.close()
            logger.info("Database connection pool closed successfully.")
        except Exception as e:
            logger.error(f"Error closing database pool: {e}")
        finally:
            db_pool = None

async def create_tables() -> None:
    """
    Create all necessary database tables if they don't exist.
    
    This function is idempotent and can be safely called multiple times.
    """
    if db_pool is None:
        logger.warning("Cannot create tables: Database pool not initialized")
        return
    
    logger.info("Creating database tables if they don't exist...")
    
    try:
        async with db_pool.acquire() as conn:
            # Enable necessary extensions
            await conn.execute("""
                CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
                CREATE EXTENSION IF NOT EXISTS "pgcrypto";
            """)
            
            # Chat history table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS chat_history (
                    message_id BIGSERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255) NOT NULL,
                    project_id VARCHAR(255),
                    user_query TEXT NOT NULL,
                    query_token INTEGER,
                    llm_token INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    channel VARCHAR(50) DEFAULT 'whatsapp',
                    response_time_ms INTEGER,
                    car_context VARCHAR(255),
                    interaction_type VARCHAR(100)
                );
            """)
            
            # User interactions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS user_interactions (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    interaction_type VARCHAR(100) NOT NULL,
                    interaction_data JSONB,
                    car_name VARCHAR(255),
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Service bookings table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS service_bookings (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    booking_type VARCHAR(50) NOT NULL,
                    customer_name VARCHAR(255),
                    phone_number VARCHAR(20),
                    email VARCHAR(255),
                    car_model VARCHAR(255),
                    preferred_date DATE,
                    preferred_time TIME,
                    message TEXT,
                    status VARCHAR(50) DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Live chat sessions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS live_chat_sessions (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    session_id VARCHAR(255),
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    agent_id VARCHAR(255),
                    status VARCHAR(50) DEFAULT 'waiting',
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ended_at TIMESTAMP,
                    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                    feedback TEXT
                );
            """)
            
            # Create indexes for better performance
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_session ON chat_history(user_id, session_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_history_timestamp ON chat_history(timestamp);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_interactions_user_id ON user_interactions(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_interactions_car_name ON user_interactions(car_name);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_service_bookings_user_id ON service_bookings(user_id);")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_live_chat_user_id ON live_chat_sessions(user_id);")
            
            logger.info("✅ Database tables created/verified successfully.")
            
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")

# ============== Helper Functions ==============

def parse_user_id(raw_user_id: str) -> Tuple[str, str, str]:
    """
    Parse composite user_id in the format: 'actualUserID_sessionID_projectID'
    Returns: (user_id_actual, session_id, project_id)
    """
    try:
        user_id_parts = raw_user_id.split('_', 2)
        if len(user_id_parts) == 3:
            return user_id_parts[0], user_id_parts[1], user_id_parts[2]
        elif len(user_id_parts) == 2:
            return user_id_parts[0], user_id_parts[1], ''
        else:
            # Single user ID, create session and project IDs
            return raw_user_id, raw_user_id, 'whatsapp'
    except Exception as e:
        logger.warning(f"Error parsing user_id {raw_user_id}: {e}")
        return raw_user_id, raw_user_id, 'whatsapp'

def extract_token_counts(response_metadata) -> Dict[str, int]:
    """
    Extract token counts from LLM response usage metadata.
    Similar to bitenxt_api.py pattern
    """
    if not response_metadata:
        return {"input_tokens": 0, "output_tokens": 0, "total_tokens": 0}

    return {
        "input_tokens": getattr(response_metadata, 'prompt_token_count', 0),
        "output_tokens": getattr(response_metadata, 'candidates_token_count', 0),
        "total_tokens": getattr(response_metadata, 'total_token_count', 0)
    }

# ============== Core Database Functions ==============

async def insert_chat_message(
    user_id: str,
    session_id: str,
    project_id: str,
    user_query: str,
    query_token: int,
    llm_response: str,
    llm_token: int,
    response_time_ms: int = None,
    car_context: str = None,
    interaction_type: str = None
) -> Optional[int]:
    """Insert chat message into database (similar to bitenxt_api.py)"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            new_message_id = await conn.fetchval(
                """
                INSERT INTO chat_history (
                    user_id, session_id, project_id, user_query,
                    query_token, llm_response, llm_token, timestamp,
                    channel, response_time_ms, car_context, interaction_type
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, NOW(), 'whatsapp', $8, $9, $10
                )
                RETURNING message_id;
                """,
                user_id, session_id, project_id, user_query,
                query_token, llm_response, llm_token, response_time_ms,
                car_context, interaction_type
            )
            return new_message_id
    except Exception as e:
        logger.error(f"Database error during message insertion: {e}")
        return None

async def insert_user_interaction(
    user_id: str,
    session_id: str,
    interaction_type: str,
    interaction_data: Dict = None,
    car_name: str = None
) -> Optional[int]:
    """Insert user interaction into database"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            interaction_id = await conn.fetchval(
                """
                INSERT INTO user_interactions (
                    user_id, session_id, interaction_type, interaction_data, car_name
                ) VALUES ($1, $2, $3, $4, $5)
                RETURNING id;
                """,
                user_id, session_id, interaction_type,
                json.dumps(interaction_data) if interaction_data else None,
                car_name
            )
            return interaction_id
    except Exception as e:
        logger.error(f"Database error during interaction insertion: {e}")
        return None

async def insert_service_booking(
    user_id: str,
    session_id: str,
    booking_type: str,
    customer_data: Dict
) -> Optional[int]:
    """Insert service booking into database"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        async with db_pool.acquire() as conn:
            booking_id = await conn.fetchval(
                """
                INSERT INTO service_bookings (
                    user_id, session_id, booking_type, customer_name, phone_number,
                    email, car_model, preferred_date, preferred_time, message
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING id;
                """,
                user_id, session_id, booking_type,
                customer_data.get("name"),
                customer_data.get("phone"),
                customer_data.get("email"),
                customer_data.get("car_model"),
                customer_data.get("preferred_date"),
                customer_data.get("preferred_time"),
                customer_data.get("message")
            )
            return booking_id
    except Exception as e:
        logger.error(f"Database error during booking insertion: {e}")
        return None

async def create_live_chat_session(user_id: str, session_id: str) -> Optional[str]:
    """Create live chat session and return session token"""
    if not ASYNCPG_AVAILABLE:
        return None

    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return None

    try:
        import uuid
        session_token = str(uuid.uuid4())
        
        async with db_pool.acquire() as conn:
            chat_session_id = await conn.fetchval(
                """
                INSERT INTO live_chat_sessions (user_id, session_id, session_token)
                VALUES ($1, $2, $3)
                RETURNING id;
                """,
                user_id, session_id, session_token
            )
            
            if chat_session_id:
                logger.info(f"✅ Live chat session {chat_session_id} created for user {user_id}")
                return session_token
            return None
    except Exception as e:
        logger.error(f"Database error during live chat session creation: {e}")
        return None

# ============== Synchronous Wrapper Functions ==============

def run_async_function(coro):
    """Run async function in sync context with performance optimization"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, we can't use run_until_complete
            # Use asyncio.create_task for better performance
            import concurrent.futures

            # Use a single thread pool executor for all async operations
            if not hasattr(run_async_function, '_executor'):
                run_async_function._executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)

            future = run_async_function._executor.submit(asyncio.run, coro)
            return future.result(timeout=10)  # Add timeout to prevent hanging
        else:
            return loop.run_until_complete(coro)
    except (RuntimeError, concurrent.futures.TimeoutError):
        # No event loop or timeout, create one with timeout
        try:
            return asyncio.wait_for(asyncio.run(coro), timeout=10)
        except asyncio.TimeoutError:
            logger.warning("Database operation timed out after 10 seconds")
            return None
        except:
            return asyncio.run(coro)

def sync_insert_chat_message(
    user_id: str,
    session_id: str,
    project_id: str,
    user_query: str,
    query_token: int,
    llm_response: str,
    llm_token: int,
    response_time_ms: int = None,
    car_context: str = None,
    interaction_type: str = None
) -> Optional[int]:
    """Synchronous wrapper for insert_chat_message"""
    return run_async_function(insert_chat_message(
        user_id, session_id, project_id, user_query, query_token,
        llm_response, llm_token, response_time_ms, car_context, interaction_type
    ))

def sync_insert_user_interaction(
    user_id: str,
    session_id: str,
    interaction_type: str,
    interaction_data: Dict = None,
    car_name: str = None
) -> Optional[int]:
    """Synchronous wrapper for insert_user_interaction"""
    return run_async_function(insert_user_interaction(
        user_id, session_id, interaction_type, interaction_data, car_name
    ))

def sync_insert_service_booking(
    user_id: str,
    session_id: str,
    booking_type: str,
    customer_data: Dict
) -> Optional[int]:
    """Synchronous wrapper for insert_service_booking"""
    return run_async_function(insert_service_booking(
        user_id, session_id, booking_type, customer_data
    ))

def sync_create_live_chat_session(user_id: str, session_id: str) -> Optional[str]:
    """Synchronous wrapper for create_live_chat_session"""
    return run_async_function(create_live_chat_session(user_id, session_id))

# ============== Database Initialization ==============

def initialize_database():
    """Initialize database connection (called on module import)"""
    try:
        run_async_function(initialize_db_pool())
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.warning(f"Database initialization failed, continuing without DB: {e}")

# Auto-initialize when module is imported
try:
    initialize_database()
except Exception as e:
    logger.warning(f"Database auto-initialization failed: {e}")

# ============== Query Functions ==============

async def get_chat_history(
    user_id: str,
    session_id: str,
    limit: int = 100,
    offset: int = 0
) -> List[Dict]:
    """Get chat history for a user session"""
    if db_pool is None:
        await initialize_db_pool()
        if db_pool is None:
            logger.warning("Database service unavailable.")
            return []

    try:
        async with db_pool.acquire() as conn:
            records = await conn.fetch(
                """
                SELECT
                    message_id, user_id, session_id, project_id,
                    user_query, query_token, llm_response, llm_token,
                    timestamp, response_time_ms, car_context, interaction_type
                FROM chat_history
                WHERE user_id = $1 AND session_id = $2
                ORDER BY timestamp ASC
                LIMIT $3 OFFSET $4;
                """,
                user_id, session_id, limit, offset
            )
            return [dict(record) for record in records]
    except Exception as e:
        logger.error(f"Database error during history retrieval: {e}")
        return []

def sync_get_chat_history(
    user_id: str,
    session_id: str,
    limit: int = 100,
    offset: int = 0
) -> List[Dict]:
    """Synchronous wrapper for get_chat_history"""
    return run_async_function(get_chat_history(user_id, session_id, limit, offset))
def update_handover(assistant_id:str, number: str, handover: bool):
    print("Updating handover status for number:", number, "to", handover)
    url = "https://llm.kiksy.live/update-handover"
    payload = {"assistant_id":assistant_id, "number": number.replace("+", "").strip(), "handover": handover }
    headers = {   "Content-Type": "application/json" }
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()  # raises error if not 2xx
        return response.json()
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}
 
async def insert_liveagentFlag(message_id: int,) -> Optional[int]:
    if db_pool is None:
        print("Database pool not initialized for insert_chat_message.")
        # Attempt to re-initialize, or handle error appropriately
        # For a robust system, this path should ideally not be hit if initialized at startup
        await initialize_db_pool()
        if db_pool is None: # Still not initialized
             raise HTTPException(status_code=503, detail="Database service unavailable.")
 
    try:
        async with db_pool.acquire() as conn:
            await conn.execute(
                """
                UPDATE chat_history
                SET agent_involved = 1
                WHERE message_id = $1;
                """,
                message_id  # the value you pass in
            )
            return {"updateStatus": "updated"}
    except Exception as e:
        print(f"Database error during message insertion: {e}")
        # Consider specific error handling, e.g., for connection errors vs. query errors
        return {"updateStatus": "Failed"}