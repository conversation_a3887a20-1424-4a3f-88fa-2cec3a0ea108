"""
Car Data Manager
Loads car data from JSON files in the 'cars' folder
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CarDataManager:
    """Manages car data loaded from JSON files in the cars folder"""
    
    def __init__(self, cars_folder: str = "cars"):
        self.cars_folder = cars_folder
        self.cars: Dict[str, Dict[str, Any]] = {}
        self.last_loaded: Optional[datetime] = None
        self.load_all_car_data()
    
    def load_all_car_data(self) -> bool:
        """Load all car JSON files from the cars folder"""
        try:
            if not os.path.exists(self.cars_folder):
                logger.warning(f"Cars folder not found: {self.cars_folder}")
                return False

            # First load cars_data.py
            try:
                from cars.cars_data import CARS_DATA
                self.cars = CARS_DATA
                logger.info("Loaded car data from cars_data.py")
            except ImportError:
                logger.warning("Could not load cars_data.py, falling back to JSON files")
                # Fall back to JSON loading if cars_data.py is not available
                for filename in os.listdir(self.cars_folder):
                    if filename.endswith(".json"):
                        file_path = os.path.join(self.cars_folder, filename)
                        try:
                            with open(file_path, "r", encoding="utf-8") as f:
                                data = json.load(f)
                                car_name = data.get("name") or os.path.splitext(filename)[0]
                                key = car_name.lower().replace(" ", "_").replace("-", "_")
                                self.cars[key] = data
                                logger.info(f"Loaded car: {car_name}")
                        except Exception as e:
                            logger.error(f"Failed to load {filename}: {e}")

            self.last_loaded = datetime.now()
            logger.info(f"Total cars loaded: {len(self.cars)}")
            return True
        except Exception as e:
            logger.error(f"Error loading car data: {e}")
            return False
    
    def get_car_data(self, car_name: str) -> Optional[Dict[str, Any]]:
        """Get car data by name (case-insensitive)"""
        key = car_name.lower().replace(" ", "_").replace("-", "_")
        return self.cars.get(key)
    
    def get_all_cars(self) -> Dict[str, Dict[str, Any]]:
        """Get all loaded cars"""
        return self.cars
    
    def search_cars(self, query: str) -> List[Dict[str, Any]]:
        """Search cars by name, category, or fuel type"""
        query_lower = query.lower()
        results = []
        for car_data in self.cars.values():
            if query_lower in car_data.get("name", "").lower() \
               or query_lower in car_data.get("category", "").lower() \
               or any(query_lower in fuel.lower() for fuel in car_data.get("fuel_types", [])):
                results.append(car_data.copy())
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get stats about loaded cars"""
        return {
            "total_cars": len(self.cars),
            "last_loaded": self.last_loaded.isoformat() if self.last_loaded else None
        }


# Singleton instance
car_data_manager = CarDataManager()

# Convenience functions
def get_car_data(car_name: str) -> Optional[Dict[str, Any]]:
    return car_data_manager.get_car_data(car_name)

def get_all_cars() -> Dict[str, Dict[str, Any]]:
    return car_data_manager.get_all_cars()

def search_cars(query: str) -> List[Dict[str, Any]]:
    return car_data_manager.search_cars(query)
